<Window x:Class="Archif.Views.FolderDocumentsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="وثائق الضبارة" Height="600" Width="900"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <Border Grid.Row="0" Margin="0,0,0,20" Padding="20" CornerRadius="10">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#4A90E2" Offset="0"/>
                    <GradientStop Color="#357ABD" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="📂" FontSize="24" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock x:Name="FolderNameText" Text="اسم الضبارة"
                          FontSize="20" FontWeight="Bold" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- شريط الأدوات -->
        <Border Grid.Row="1" Margin="0,0,0,20" Padding="15"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- زر إضافة وثيقة -->
                <Button x:Name="AddDocumentButton" Grid.Column="0"
                       Background="#28A745" Foreground="White"
                       BorderThickness="0" Padding="20,12"
                       Click="AddDocumentButton_Click">
                    <Button.Effect>
                        <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                    </Button.Effect>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة وثيقة جديدة" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <!-- البحث -->
                <Grid Grid.Column="1" Margin="20,0">
                    <TextBox x:Name="SearchTextBox"
                            Width="300" Height="35" Padding="10,8"
                            BorderBrush="#E0E0E0" BorderThickness="1"
                            TextChanged="SearchTextBox_TextChanged"/>
                    <TextBlock x:Name="SearchPlaceholder"
                              Text="البحث في الوثائق..."
                              Foreground="#999999"
                              IsHitTestVisible="False"
                              VerticalAlignment="Center"
                              HorizontalAlignment="Left"
                              Margin="15,0,0,0"/>
                </Grid>

                <!-- إحصائية الوثائق -->
                <Border Grid.Column="2" Padding="15,8" Background="#F8F9FA"
                       BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📄" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock x:Name="DocumentsCountText" Text="0 وثيقة"
                                  FontWeight="Bold" Foreground="#333333"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- قائمة الوثائق -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0"
               BorderThickness="1" CornerRadius="8" Padding="10">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <ItemsControl x:Name="DocumentsItemsControl">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Margin="0,0,0,15" Padding="20"
                                   Background="White" BorderBrush="#E0E0E0"
                                   BorderThickness="1" CornerRadius="12">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                                <Setter Property="BorderBrush" Value="#4A90E2"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- أيقونة نوع الوثيقة -->
                                    <Border Grid.Column="0" CornerRadius="15" Width="30" Height="30"
                                           VerticalAlignment="Center" Margin="0,0,15,0">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Setter Property="Background" Value="#28A745"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Type}" Value="Outgoing">
                                                        <Setter Property="Background" Value="#DC3545"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="White" FontWeight="Bold" FontSize="12">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Text" Value="📥"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Type}" Value="Outgoing">
                                                            <Setter Property="Text" Value="📤"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>

                                    <!-- معلومات الوثيقة -->
                                    <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Subject}" FontSize="16" FontWeight="Bold"
                                                  Foreground="#333333" TextTrimming="CharacterEllipsis"
                                                  HorizontalAlignment="Center" TextAlignment="Center"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0" HorizontalAlignment="Center">
                                            <Border Background="#4A90E2" CornerRadius="12" Padding="8,4" Margin="2">
                                                <TextBlock Text="{Binding DocumentNumber}" FontSize="11"
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Background="#28A745" CornerRadius="12" Padding="8,4" Margin="2">
                                                <TextBlock Text="{Binding DocumentDate, StringFormat=\{0:dd/MM/yyyy\}}"
                                                          FontSize="11" Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Background="#FF9800" CornerRadius="12" Padding="8,4" Margin="2">
                                                <TextBlock Text="{Binding FromTo}" FontSize="11"
                                                          Foreground="White" FontWeight="Bold"
                                                          TextTrimming="CharacterEllipsis" MaxWidth="120"/>
                                            </Border>
                                        </StackPanel>
                                    </StackPanel>

                                    <!-- عدد المرفقات -->
                                    <Border Grid.Column="2" Background="#FF9800" CornerRadius="10"
                                           Padding="8,4" VerticalAlignment="Center" Margin="10,0">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📎" FontSize="12" Foreground="White" Margin="0,0,3,0"/>
                                            <TextBlock Text="{Binding AttachmentsCount}" FontSize="12"
                                                      Foreground="White" FontWeight="Bold"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- أزرار العمليات -->
                                    <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <Button Content="👁️" ToolTip="عرض الوثيقة"
                                               Background="#17A2B8" Foreground="White"
                                               BorderThickness="0" Width="35" Height="35"
                                               Margin="3"
                                               Click="ViewDocumentButton_Click"
                                               Tag="{Binding}">
                                            <Button.Effect>
                                                <DropShadowEffect Color="#17A2B8" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                            </Button.Effect>
                                        </Button>
                                        <Button Content="📝" ToolTip="تعديل الوثيقة"
                                               Background="#4A90E2" Foreground="White"
                                               BorderThickness="0" Width="35" Height="35"
                                               Margin="3"
                                               Click="EditDocumentButton_Click"
                                               Tag="{Binding}">
                                            <Button.Effect>
                                                <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                            </Button.Effect>
                                        </Button>
                                        <Button Content="🗑️" ToolTip="حذف الوثيقة"
                                               Background="#DC3545" Foreground="White"
                                               BorderThickness="0" Width="35" Height="35"
                                               Margin="3"
                                               Click="DeleteDocumentButton_Click"
                                               Tag="{Binding}">
                                            <Button.Effect>
                                                <DropShadowEffect Color="#DC3545" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                            </Button.Effect>
                                        </Button>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Border>

        <!-- أزرار النافذة -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="CloseButton" Content="🚪 إغلاق"
                   Background="#6C757D" Foreground="White"
                   BorderThickness="0" Padding="20,12"
                   FontWeight="Bold" Click="CloseButton_Click">
                <Button.Effect>
                    <DropShadowEffect Color="#6C757D" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                </Button.Effect>
            </Button>
        </StackPanel>
    </Grid>
</Window>
