<Window x:Class="Archif.Views.EditDocumentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل الوثيقة"
        Width="1000" Height="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <Border Grid.Row="0" Margin="0,0,0,20" Padding="20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📝" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitleText" Text="تعديل الوثيقة"
                          FontSize="20" FontWeight="Bold" VerticalAlignment="Center"
                          Foreground="#333333"/>
            </StackPanel>
        </Border>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- معلومات الوثيقة الأساسية -->
                <Border Margin="0,0,0,20" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="📋" FontSize="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="المعلومات الأساسية"
                                      FontSize="16" FontWeight="Bold"
                                      VerticalAlignment="Center" Foreground="#333333"/>
                        </StackPanel>

                        <!-- الحقول -->
                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- رقم الوثيقة -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="رقم الوثيقة *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <TextBox x:Name="DocumentNumberTextBox" Height="35" Padding="10,8"
                                        BorderBrush="#E0E0E0" BorderThickness="1"/>
                            </StackPanel>

                            <!-- تاريخ الوثيقة -->
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,15">
                                <TextBlock Text="تاريخ الوثيقة *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <DatePicker x:Name="DocumentDatePicker" Height="35"
                                           BorderBrush="#E0E0E0" BorderThickness="1"/>
                            </StackPanel>

                            <!-- نوع الوثيقة -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="نوع الوثيقة *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <RadioButton x:Name="OutgoingRadio" Content="صادر" Margin="0,0,20,0"
                                                GroupName="DocumentType" Checked="DocumentType_Changed"/>
                                    <RadioButton x:Name="IncomingRadio" Content="وارد"
                                                GroupName="DocumentType" Checked="DocumentType_Changed"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- تسلسل الحفظ -->
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,15">
                                <TextBlock Text="تسلسل الحفظ *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <TextBox x:Name="ArchiveSequenceTextBox" Height="35" Padding="10,8"
                                        BorderBrush="#E0E0E0" BorderThickness="1" Foreground="Red"
                                        TextChanged="ArchiveSequenceTextBox_TextChanged"/>
                                <TextBlock Text="أدخل رقم التسلسل فقط (مثال: 1، 2، 3...)"
                                          FontSize="11" Foreground="#999999" Margin="0,3,0,0"/>
                            </StackPanel>

                            <!-- القسم -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="القسم *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <ComboBox x:Name="DepartmentComboBox" Height="35"
                                         BorderBrush="#E0E0E0" BorderThickness="1"
                                         DisplayMemberPath="Name" SelectedValuePath="Id"
                                         SelectionChanged="DepartmentComboBox_SelectionChanged"/>
                            </StackPanel>

                            <!-- الضبارة -->
                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,15">
                                <TextBlock Text="الضبارة *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <ComboBox x:Name="FolderComboBox" Height="35"
                                         BorderBrush="#E0E0E0" BorderThickness="1"
                                         DisplayMemberPath="Name" SelectedValuePath="Id"/>
                            </StackPanel>

                            <!-- الجهة -->
                            <StackPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,15">
                                <TextBlock Text="الجهة" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <ComboBox x:Name="OrganizationComboBox" Height="35"
                                         BorderBrush="#E0E0E0" BorderThickness="1"
                                         DisplayMemberPath="Name" SelectedValuePath="Id"/>
                            </StackPanel>

                            <!-- الموضوع -->
                            <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,15">
                                <TextBlock Text="الموضوع *" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <TextBox x:Name="SubjectTextBox" Height="80" Padding="10,8"
                                        BorderBrush="#E0E0E0" BorderThickness="1"
                                        TextWrapping="Wrap" AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>

                            <!-- التسلسل التلقائي (للقراءة فقط) -->
                            <StackPanel Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,0">
                                <TextBlock Text="التسلسل التلقائي" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <TextBox x:Name="SequenceTextBox" Height="35" Padding="10,8"
                                        BorderBrush="#E0E0E0" BorderThickness="1"
                                        IsReadOnly="True" Background="#F8F9FA"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!-- قسم المرفقات -->
                <Border Margin="0,0,0,20" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان المرفقات -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="📎" FontSize="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="المرفقات والملفات الممسوحة ضوئياً"
                                      FontSize="16" FontWeight="Bold"
                                      VerticalAlignment="Center" Foreground="#333333"/>
                        </StackPanel>

                        <!-- أزرار إدارة الملفات -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal"
                                   HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Button x:Name="AddFilesButton"
                                   Background="#4A90E2" Foreground="White"
                                   BorderThickness="0" Padding="15,8"
                                   Margin="5" Click="AddFilesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📁" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="إضافة ملفات" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ScanButton"
                                   Background="White" Foreground="#4A90E2"
                                   BorderBrush="#4A90E2" BorderThickness="1"
                                   Padding="15,8" Margin="5"
                                   Click="ScanButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📷" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="مسح ضوئي" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="AddFromCameraButton"
                                   Background="White" Foreground="#28A745"
                                   BorderBrush="#28A745" BorderThickness="1"
                                   Padding="15,8" Margin="5"
                                   Click="AddFromCameraButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📸" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="من الكاميرا" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- قائمة الملفات المرفقة -->
                        <ListBox Grid.Row="2" x:Name="AttachmentsListBox"
                                Background="Transparent" BorderThickness="0"
                                MinHeight="150" MaxHeight="300">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,2" Padding="10"
                                           Background="#F8F9FA" BorderBrush="#E0E0E0"
                                           BorderThickness="1" CornerRadius="4">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="{Binding FileIcon}" FontSize="20"
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>

                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding FileName}" FontWeight="Bold"
                                                          Foreground="#333333"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding TypeDisplay}" FontSize="12"
                                                              Foreground="#666666" Margin="0,0,10,0"/>
                                                    <TextBlock Text="{Binding FileSizeFormatted}" FontSize="12"
                                                              Foreground="#666666"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="👁️" ToolTip="معاينة"
                                                   Background="Transparent" Foreground="#4A90E2"
                                                   BorderThickness="0" Width="30" Height="30"
                                                   Click="PreviewAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="3" Content="📂" ToolTip="فتح"
                                                   Background="Transparent" Foreground="#28A745"
                                                   BorderThickness="0" Width="30" Height="30"
                                                   Click="OpenAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="4" Content="🗑️" ToolTip="حذف"
                                                   Background="Transparent" Foreground="#DC3545"
                                                   BorderThickness="0" Width="30" Height="30"
                                                   Click="RemoveAttachment_Click" Tag="{Binding}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار النافذة -->
        <Border Grid.Row="2" Margin="0,20,0,0" Padding="20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Background="#28A745" Foreground="White"
                       BorderThickness="0" Padding="20,10"
                       Margin="10" Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ التعديلات" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>

                <Button x:Name="CancelButton"
                       Background="#6C757D" Foreground="White"
                       BorderThickness="0" Padding="20,10"
                       Margin="10" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
