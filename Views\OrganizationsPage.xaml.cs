using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using Archif.Services;
using Archif.Models;
using WpfUserControl = System.Windows.Controls.UserControl;
using WpfMessageBox = System.Windows.MessageBox;
using WpfButton = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// صفحة إدارة الجهات
    /// </summary>
    public partial class OrganizationsPage : WpfUserControl
    {
        private readonly DatabaseService _databaseService;
        private readonly ObservableCollection<OrganizationViewModel> _organizations;
        private List<Organization> _allOrganizations = new();

        public OrganizationsPage(DatabaseService databaseService)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _organizations = new ObservableCollection<OrganizationViewModel>();

            OrganizationsDataGrid.ItemsSource = _organizations;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                await LoadOrganizations();
                await LoadStatistics();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadOrganizations()
        {
            _allOrganizations = await _databaseService.GetOrganizationsAsync();
            await RefreshOrganizationsList();
        }

        private async Task RefreshOrganizationsList()
        {
            _organizations.Clear();

            var filteredOrganizations = _allOrganizations.AsEnumerable();

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchText = SearchTextBox.Text.ToLower();
                filteredOrganizations = filteredOrganizations.Where(o =>
                    o.Name.ToLower().Contains(searchText) ||
                    (o.Description?.ToLower().Contains(searchText) ?? false));
            }

            // تطبيق فلتر النوع
            if (TypeFilterComboBox.SelectedIndex > 0)
            {
                var selectedType = (OrganizationType)(TypeFilterComboBox.SelectedIndex - 1);
                filteredOrganizations = filteredOrganizations.Where(o => o.Type == selectedType);
            }

            foreach (var org in filteredOrganizations)
            {
                var documentsCount = await _databaseService.GetDocumentsByOrganizationCountAsync(org.Id);
                _organizations.Add(new OrganizationViewModel
                {
                    Id = org.Id,
                    Name = org.Name,
                    Description = org.Description,
                    Type = org.Type,
                    TypeDisplay = GetTypeDisplayName(org.Type),
                    CreatedDate = org.CreatedDate,
                    DocumentsCount = documentsCount
                });
            }
        }

        private async Task LoadStatistics()
        {
            try
            {
                var totalCount = await _databaseService.GetOrganizationsCountAsync();
                TotalOrganizationsText.Text = totalCount.ToString();

                var governmentCount = _allOrganizations.Count(o => o.Type == OrganizationType.Government);
                GovernmentOrganizationsText.Text = governmentCount.ToString();

                var privateCount = _allOrganizations.Count(o => o.Type == OrganizationType.Private);
                PrivateOrganizationsText.Text = privateCount.ToString();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetTypeDisplayName(OrganizationType type)
        {
            return type switch
            {
                OrganizationType.Government => "حكومية",
                OrganizationType.Private => "خاصة",
                OrganizationType.Academic => "أكاديمية",
                OrganizationType.NonProfit => "غير ربحية",
                OrganizationType.International => "دولية",
                _ => "غير محدد"
            };
        }

        private void AddOrganizationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new AddEditOrganizationWindow(_databaseService);
                if (addWindow.ShowDialog() == true)
                {
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في فتح نافذة الإضافة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void EditOrganization_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is WpfButton button && button.Tag is OrganizationViewModel orgViewModel)
                {
                    var organization = await _databaseService.GetOrganizationByIdAsync(orgViewModel.Id);
                    if (organization != null)
                    {
                        var editWindow = new AddEditOrganizationWindow(_databaseService, organization);
                        if (editWindow.ShowDialog() == true)
                        {
                            LoadData();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في فتح نافذة التحرير: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteOrganization_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is WpfButton button && button.Tag is OrganizationViewModel orgViewModel)
                {
                    var result = WpfMessageBox.Show($"هل أنت متأكد من حذف الجهة '{orgViewModel.Name}'؟\n\nملاحظة: سيتم إلغاء ربط جميع الوثائق بهذه الجهة.",
                                               "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        await _databaseService.DeleteOrganizationAsync(orgViewModel.Id);
                        LoadData();

                        WpfMessageBox.Show("تم حذف الجهة بنجاح", "نجح الحذف",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في حذف الجهة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            await RefreshOrganizationsList();
        }

        private async void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allOrganizations.Any())
            {
                await RefreshOrganizationsList();
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }
    }

    /// <summary>
    /// نموذج عرض الجهة
    /// </summary>
    public class OrganizationViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public OrganizationType Type { get; set; }
        public string TypeDisplay { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public int DocumentsCount { get; set; }
    }
}
