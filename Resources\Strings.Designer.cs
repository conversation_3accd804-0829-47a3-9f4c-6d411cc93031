//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Archif.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Strings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Strings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Archif.Resources.Strings", typeof(Strings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly-typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إضافة.
        /// </summary>
        internal static string Action_Add {
            get {
                return ResourceManager.GetString("Action_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إلغاء.
        /// </summary>
        internal static string Action_Cancel {
            get {
                return ResourceManager.GetString("Action_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف.
        /// </summary>
        internal static string Action_Delete {
            get {
                return ResourceManager.GetString("Action_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحرير.
        /// </summary>
        internal static string Action_Edit {
            get {
                return ResourceManager.GetString("Action_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تصدير.
        /// </summary>
        internal static string Action_Export {
            get {
                return ResourceManager.GetString("Action_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استيراد.
        /// </summary>
        internal static string Action_Import {
            get {
                return ResourceManager.GetString("Action_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحديث.
        /// </summary>
        internal static string Action_Refresh {
            get {
                return ResourceManager.GetString("Action_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حفظ.
        /// </summary>
        internal static string Action_Save {
            get {
                return ResourceManager.GetString("Action_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بحث.
        /// </summary>
        internal static string Action_Search {
            get {
                return ResourceManager.GetString("Action_Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عرض.
        /// </summary>
        internal static string Action_View {
            get {
                return ResourceManager.GetString("Action_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نظام احترافي لإدارة الوثائق الصادرة والواردة.
        /// </summary>
        internal static string AppDescription {
            get {
                return ResourceManager.GetString("AppDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نظام الأرشفة الإلكترونية.
        /// </summary>
        internal static string AppName {
            get {
                return ResourceManager.GetString("AppName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الإصدار 2.0.0.
        /// </summary>
        internal static string AppVersion {
            get {
                return ResourceManager.GetString("AppVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الإضافة بنجاح.
        /// </summary>
        internal static string Msg_AddSuccess {
            get {
                return ResourceManager.GetString("Msg_AddSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكيد.
        /// </summary>
        internal static string Msg_Confirm {
            get {
                return ResourceManager.GetString("Msg_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من الحذف؟.
        /// </summary>
        internal static string Msg_DeleteConfirm {
            get {
                return ResourceManager.GetString("Msg_DeleteConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        internal static string Msg_Error {
            get {
                return ResourceManager.GetString("Msg_Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ في التحميل.
        /// </summary>
        internal static string Msg_LoadError {
            get {
                return ResourceManager.GetString("Msg_LoadError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم بنجاح.
        /// </summary>
        internal static string Msg_Success {
            get {
                return ResourceManager.GetString("Msg_Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        internal static string Msg_Warning {
            get {
                return ResourceManager.GetString("Msg_Warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الرئيسية.
        /// </summary>
        internal static string Nav_Home {
            get {
                return ResourceManager.GetString("Nav_Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الإعدادات.
        /// </summary>
        internal static string Nav_Settings {
            get {
                return ResourceManager.GetString("Nav_Settings", resourceCulture);
            }
        }
    }
}
