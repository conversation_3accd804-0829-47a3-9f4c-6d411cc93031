using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using Archif.Constants;
using Application = System.Windows.Application;

namespace Archif.Helpers
{
    /// <summary>
    /// نظام معالجة الأخطاء المحسن مع التسجيل والتتبع
    /// </summary>
    public static class ErrorHandler
    {
        private static readonly string LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "Archif", "Logs");

        private static readonly object LogLock = new object();

        static ErrorHandler()
        {
            try
            {
                Directory.CreateDirectory(LogDirectory);
            }
            catch
            {
                // في حالة فشل إنشاء مجلد السجلات، نتجاهل الخطأ
            }
        }

        /// <summary>
        /// عرض رسالة خطأ بسيطة مع التسجيل
        /// </summary>
        public static void ShowError(string message, string title = "خطأ")
        {
            LogError(new Exception(message), title);
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة خطأ مع تفاصيل الاستثناء والتسجيل التلقائي
        /// </summary>
        public static void ShowError(Exception ex, string context = "")
        {
            var userMessage = GetUserFriendlyMessage(ex, context);
            LogError(ex, context);

            System.Windows.MessageBox.Show(userMessage, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة خطأ مع تفاصيل كاملة للتطوير
        /// </summary>
        public static void ShowDetailedError(Exception ex, string context = "")
        {
            LogError(ex, context);

            var message = string.IsNullOrEmpty(context)
                ? $"حدث خطأ: {ex.Message}\n\nتفاصيل: {ex.StackTrace}"
                : $"خطأ في {context}: {ex.Message}\n\nتفاصيل: {ex.StackTrace}";

            System.Windows.MessageBox.Show(message, "خطأ تفصيلي", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة نجح
        /// </summary>
        public static void ShowSuccess(string message, string title = "نجح")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض رسالة تحذير مع التسجيل
        /// </summary>
        public static void ShowWarning(string message, string title = "تحذير")
        {
            LogWarning(message, title);
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// عرض رسالة تأكيد
        /// </summary>
        public static bool ShowConfirmation(string message, string title = "تأكيد")
        {
            var result = System.Windows.MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// عرض رسالة مع خيار إعادة المحاولة
        /// </summary>
        public static bool ShowRetryOption(string message, string title = "خطأ")
        {
            var result = System.Windows.MessageBox.Show($"{message}\n\nهل تريد إعادة المحاولة؟",
                                       title,
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// تسجيل خطأ في ملف السجل
        /// </summary>
        public static void LogError(Exception ex, string context = "")
        {
            try
            {
                var logEntry = new
                {
                    Timestamp = DateTime.Now,
                    Level = "ERROR",
                    Context = context,
                    Message = ex.Message,
                    StackTrace = ex.StackTrace,
                    InnerException = ex.InnerException?.Message,
                    MachineName = Environment.MachineName,
                    UserName = Environment.UserName
                };

                WriteLogEntry(logEntry);
            }
            catch
            {
                // في حالة فشل التسجيل، لا نريد أن نسبب خطأ إضافي
            }
        }

        /// <summary>
        /// تسجيل تحذير في ملف السجل
        /// </summary>
        public static void LogWarning(string message, string context = "")
        {
            try
            {
                var logEntry = new
                {
                    Timestamp = DateTime.Now,
                    Level = "WARNING",
                    Context = context,
                    Message = message,
                    MachineName = Environment.MachineName,
                    UserName = Environment.UserName
                };

                WriteLogEntry(logEntry);
            }
            catch
            {
                // في حالة فشل التسجيل، لا نريد أن نسبب خطأ إضافي
            }
        }

        /// <summary>
        /// تسجيل معلومات في ملف السجل
        /// </summary>
        public static void LogInfo(string message, string context = "")
        {
            try
            {
                var logEntry = new
                {
                    Timestamp = DateTime.Now,
                    Level = "INFO",
                    Context = context,
                    Message = message,
                    MachineName = Environment.MachineName,
                    UserName = Environment.UserName
                };

                WriteLogEntry(logEntry);
            }
            catch
            {
                // في حالة فشل التسجيل، لا نريد أن نسبب خطأ إضافي
            }
        }

        /// <summary>
        /// كتابة إدخال السجل إلى الملف
        /// </summary>
        private static void WriteLogEntry(object logEntry)
        {
            lock (LogLock)
            {
                try
                {
                    var logFileName = $"archif_log_{DateTime.Now:yyyyMMdd}.json";
                    var logFilePath = Path.Combine(LogDirectory, logFileName);

                    var logText = JsonSerializer.Serialize(logEntry, new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });

                    File.AppendAllText(logFilePath, logText + Environment.NewLine);

                    // تنظيف السجلات القديمة (أكثر من 30 يوم)
                    CleanOldLogs();
                }
                catch
                {
                    // في حالة فشل الكتابة، لا نريد أن نسبب خطأ إضافي
                }
            }
        }

        /// <summary>
        /// تنظيف السجلات القديمة
        /// </summary>
        private static void CleanOldLogs()
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-30);
                var logFiles = Directory.GetFiles(LogDirectory, "archif_log_*.json");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch
            {
                // في حالة فشل التنظيف، لا نوقف التطبيق
            }
        }

        /// <summary>
        /// تحويل رسالة الخطأ التقنية إلى رسالة مفهومة للمستخدم
        /// </summary>
        private static string GetUserFriendlyMessage(Exception ex, string context)
        {
            var baseMessage = ex switch
            {
                UnauthorizedAccessException => "ليس لديك صلاحية للوصول إلى هذا المورد.",
                FileNotFoundException => "الملف المطلوب غير موجود.",
                DirectoryNotFoundException => "المجلد المطلوب غير موجود.",
                IOException => "خطأ في عملية القراءة أو الكتابة.",
                OutOfMemoryException => "الذاكرة المتاحة غير كافية.",
                TimeoutException => "انتهت مهلة العملية.",
                InvalidOperationException => "العملية المطلوبة غير صالحة في الوقت الحالي.",
                ArgumentException => "البيانات المدخلة غير صحيحة.",
                System.Data.Common.DbException => "خطأ في قاعدة البيانات.",
                System.Net.NetworkInformation.NetworkInformationException => "خطأ في الاتصال بالشبكة.",
                _ => "حدث خطأ غير متوقع."
            };

            return string.IsNullOrEmpty(context)
                ? baseMessage
                : $"{baseMessage}\n\nالسياق: {context}";
        }

        /// <summary>
        /// معالجة خطأ غير متزامن مع التسجيل
        /// </summary>
        public static async Task HandleErrorAsync(Exception ex, string context = "")
        {
            await Task.Run(() => LogError(ex, context));

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                ShowError(ex, context);
            });
        }

        /// <summary>
        /// تنفيذ عملية مع معالجة الأخطاء التلقائية
        /// </summary>
        public static async Task<T> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> operation,
            string context = "",
            T defaultValue = default(T))
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex, context);
                return defaultValue;
            }
        }

        /// <summary>
        /// تنفيذ عملية مع معالجة الأخطاء التلقائية (بدون قيمة إرجاع)
        /// </summary>
        public static async Task ExecuteWithErrorHandlingAsync(
            Func<Task> operation,
            string context = "")
        {
            try
            {
                await operation();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex, context);
            }
        }
    }
}
