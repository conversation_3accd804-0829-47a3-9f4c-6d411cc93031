using System;
using System.Windows;
using Archif.Constants;

namespace Archif.Helpers
{
    /// <summary>
    /// كلاس مساعد لمعالجة الأخطاء بشكل موحد
    /// </summary>
    public static class ErrorHandler
    {
        /// <summary>
        /// عرض رسالة خطأ بسيطة
        /// </summary>
        public static void ShowError(string message, string title = "خطأ")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة خطأ مع تفاصيل الاستثناء
        /// </summary>
        public static void ShowError(Exception ex, string context = "")
        {
            var message = string.IsNullOrEmpty(context)
                ? $"حدث خطأ: {ex.Message}"
                : $"خطأ في {context}: {ex.Message}";

            System.Windows.MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة خطأ مع تفاصيل كاملة للتطوير
        /// </summary>
        public static void ShowDetailedError(Exception ex, string context = "")
        {
            var message = string.IsNullOrEmpty(context)
                ? $"حدث خطأ: {ex.Message}\n\nتفاصيل: {ex.StackTrace}"
                : $"خطأ في {context}: {ex.Message}\n\nتفاصيل: {ex.StackTrace}";

            System.Windows.MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة نجح
        /// </summary>
        public static void ShowSuccess(string message, string title = "نجح")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض رسالة تحذير
        /// </summary>
        public static void ShowWarning(string message, string title = "تحذير")
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// عرض رسالة تأكيد
        /// </summary>
        public static bool ShowConfirmation(string message, string title = "تأكيد")
        {
            var result = System.Windows.MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// عرض رسالة مع خيار إعادة المحاولة
        /// </summary>
        public static bool ShowRetryOption(string message, string title = "خطأ")
        {
            var result = System.Windows.MessageBox.Show($"{message}\n\nهل تريد إعادة المحاولة؟",
                                       title,
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }
    }
}
