using System.IO;
using System.Security.Cryptography;
using Archif.Models;

namespace Archif.Services
{
    /// <summary>
    /// خدمة إدارة الملفات والمرفقات
    /// </summary>
    public class FileService
    {
        private readonly string _attachmentsPath;
        private readonly string _scannedPath;
        private readonly string _tempPath;

        public FileService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var archifPath = Path.Combine(appDataPath, "Archif");
            
            _attachmentsPath = Path.Combine(archifPath, "Attachments");
            _scannedPath = Path.Combine(archifPath, "Scanned");
            _tempPath = Path.Combine(archifPath, "Temp");

            // إنشاء المجلدات إذا لم تكن موجودة
            EnsureDirectoriesExist();
        }

        private void EnsureDirectoriesExist()
        {
            Directory.CreateDirectory(_attachmentsPath);
            Directory.CreateDirectory(_scannedPath);
            Directory.CreateDirectory(_tempPath);
        }

        /// <summary>
        /// حفظ ملف مرفق
        /// </summary>
        public async Task<AttachmentInfo> SaveAttachmentAsync(string sourceFilePath, int documentId, AttachmentType type = AttachmentType.Document, string? description = null)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException("الملف المصدر غير موجود");

                var fileName = Path.GetFileName(sourceFilePath);
                var fileExtension = Path.GetExtension(sourceFilePath);
                var fileInfo = new FileInfo(sourceFilePath);

                // إنشاء اسم ملف فريد
                var uniqueFileName = GenerateUniqueFileName(fileName, documentId);
                var targetPath = type == AttachmentType.ScannedImage || type == AttachmentType.ScannedPdf 
                    ? Path.Combine(_scannedPath, uniqueFileName)
                    : Path.Combine(_attachmentsPath, uniqueFileName);

                // نسخ الملف
                await CopyFileAsync(sourceFilePath, targetPath);

                // إنشاء معلومات المرفق
                var attachmentInfo = new AttachmentInfo
                {
                    FileName = fileName,
                    FilePath = targetPath,
                    FileType = fileExtension,
                    MimeType = GetMimeType(fileExtension),
                    FileSize = fileInfo.Length,
                    Type = type,
                    Description = description,
                    CreatedDate = DateTime.Now
                };

                return attachmentInfo;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في حفظ المرفق: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ ملفات متعددة
        /// </summary>
        public async Task<List<AttachmentInfo>> SaveMultipleAttachmentsAsync(string[] sourceFilePaths, int documentId, AttachmentType type = AttachmentType.Document)
        {
            var attachments = new List<AttachmentInfo>();

            foreach (var filePath in sourceFilePaths)
            {
                try
                {
                    var attachment = await SaveAttachmentAsync(filePath, documentId, type);
                    attachments.Add(attachment);
                }
                catch (Exception ex)
                {
                    // تسجيل الخطأ ومتابعة باقي الملفات
                    System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الملف {filePath}: {ex.Message}");
                }
            }

            return attachments;
        }

        /// <summary>
        /// حذف ملف مرفق
        /// </summary>
        public async Task<bool> DeleteAttachmentAsync(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    await Task.Run(() => File.Delete(filePath));
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فتح ملف مرفق
        /// </summary>
        public async Task<bool> OpenAttachmentAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                await Task.Run(() =>
                {
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    };
                    System.Diagnostics.Process.Start(startInfo);
                });

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء اسم ملف فريد
        /// </summary>
        private string GenerateUniqueFileName(string originalFileName, int documentId)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            
            return $"DOC{documentId}_{timestamp}_{uniqueId}_{nameWithoutExtension}{extension}";
        }

        /// <summary>
        /// نسخ ملف بشكل غير متزامن
        /// </summary>
        private async Task CopyFileAsync(string sourceFile, string destinationFile)
        {
            using var sourceStream = new FileStream(sourceFile, FileMode.Open, FileAccess.Read);
            using var destinationStream = new FileStream(destinationFile, FileMode.Create, FileAccess.Write);
            await sourceStream.CopyToAsync(destinationStream);
        }

        /// <summary>
        /// الحصول على نوع MIME للملف
        /// </summary>
        private string GetMimeType(string extension)
        {
            return extension.ToLower() switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                ".txt" => "text/plain",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                ".7z" => "application/x-7z-compressed",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// التحقق من صحة نوع الملف
        /// </summary>
        public bool IsValidFileType(string fileName)
        {
            var allowedExtensions = new[]
            {
                ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif",
                ".txt", ".zip", ".rar", ".7z"
            };

            var extension = Path.GetExtension(fileName).ToLower();
            return allowedExtensions.Contains(extension);
        }

        /// <summary>
        /// الحصول على حجم الملف المنسق
        /// </summary>
        public string GetFormattedFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }

        /// <summary>
        /// الحصول على مسار مجلد المرفقات
        /// </summary>
        public string GetAttachmentsPath() => _attachmentsPath;

        /// <summary>
        /// الحصول على مسار مجلد الملفات الممسوحة
        /// </summary>
        public string GetScannedPath() => _scannedPath;

        /// <summary>
        /// الحصول على مسار مجلد الملفات المؤقتة
        /// </summary>
        public string GetTempPath() => _tempPath;

        /// <summary>
        /// تنظيف الملفات المؤقتة
        /// </summary>
        public async Task CleanupTempFilesAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    if (Directory.Exists(_tempPath))
                    {
                        var files = Directory.GetFiles(_tempPath);
                        foreach (var file in files)
                        {
                            try
                            {
                                var fileInfo = new FileInfo(file);
                                if (fileInfo.CreationTime < DateTime.Now.AddDays(-1))
                                {
                                    File.Delete(file);
                                }
                            }
                            catch
                            {
                                // تجاهل أخطاء حذف الملفات المؤقتة
                            }
                        }
                    }
                });
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }
    }
}
