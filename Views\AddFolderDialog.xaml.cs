using System.Windows;
using System.Windows.Controls;
using Archif.Models;
using Archif.Helpers;

namespace Archif.Views
{
    /// <summary>
    /// نافذة إضافة/تحرير ضبارة جديدة
    /// </summary>
    public partial class AddFolderDialog : Window
    {
        public string FolderName => FolderNameTextBox.Text.Trim();
        public int SelectedDepartmentId => (int)(DepartmentComboBox.SelectedValue ?? 0);
        public DateTime CreatedDate => CreatedDatePicker.SelectedDate ?? DateTime.Now;
        public string Notes => NotesTextBox.Text.Trim();

        private readonly bool _isEditMode;

        public AddFolderDialog(IEnumerable<Department> departments, string folderName = "", int selectedDepartmentId = 0)
        {
            InitializeComponent();
            _isEditMode = !string.IsNullOrEmpty(folderName);

            InitializeForm(departments, folderName, selectedDepartmentId);
        }

        private void InitializeForm(IEnumerable<Department> departments, string folderName, int selectedDepartmentId)
        {
            try
            {
                // تعيين العنوان حسب الوضع
                Title = _isEditMode ? "تعديل الضبارة" : "إضافة ضبارة جديدة";

                // تعيين التاريخ الحالي كافتراضي
                CreatedDatePicker.SelectedDate = DateTime.Now;

                // تحميل الأقسام
                DepartmentComboBox.ItemsSource = departments;

                // تعيين القيم الافتراضية
                FolderNameTextBox.Text = folderName;

                if (selectedDepartmentId > 0)
                {
                    DepartmentComboBox.SelectedValue = selectedDepartmentId;
                }
                else if (departments.Any())
                {
                    DepartmentComboBox.SelectedIndex = 0;
                }

                // تركيز على حقل الاسم
                FolderNameTextBox.SelectAll();
                FolderNameTextBox.Focus();

                // ربط أحداث التحقق
                FolderNameTextBox.TextChanged += FolderNameTextBox_TextChanged;
                DepartmentComboBox.SelectionChanged += DepartmentComboBox_SelectionChanged;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تهيئة النافذة");
            }
        }

        private void FolderNameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateFolderName();
        }

        private void DepartmentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ValidateDepartment();
        }

        private bool ValidateFolderName()
        {
            var name = FolderNameTextBox.Text.Trim();

            if (string.IsNullOrWhiteSpace(name))
            {
                NameValidationText.Text = "يرجى إدخال اسم الضبارة";
                NameValidationText.Visibility = Visibility.Visible;
                FolderNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else if (name.Length < 2)
            {
                NameValidationText.Text = "اسم الضبارة يجب أن يكون أكثر من حرف واحد";
                NameValidationText.Visibility = Visibility.Visible;
                FolderNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else if (name.Length > 200)
            {
                NameValidationText.Text = "اسم الضبارة طويل جداً (الحد الأقصى 200 حرف)";
                NameValidationText.Visibility = Visibility.Visible;
                FolderNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else
            {
                NameValidationText.Visibility = Visibility.Collapsed;
                FolderNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                return true;
            }
        }

        private bool ValidateDepartment()
        {
            if (DepartmentComboBox.SelectedValue == null)
            {
                DepartmentValidationText.Text = "يرجى اختيار القسم";
                DepartmentValidationText.Visibility = Visibility.Visible;
                DepartmentComboBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else
            {
                DepartmentValidationText.Visibility = Visibility.Collapsed;
                DepartmentComboBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                return true;
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                bool isNameValid = ValidateFolderName();
                bool isDepartmentValid = ValidateDepartment();

                if (!isNameValid)
                {
                    FolderNameTextBox.Focus();
                    return;
                }

                if (!isDepartmentValid)
                {
                    DepartmentComboBox.Focus();
                    return;
                }

                // تعطيل الأزرار أثناء الحفظ
                OkButton.IsEnabled = false;
                OkButton.Content = "⏳ جاري الحفظ...";

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "حفظ الضبارة");
            }
            finally
            {
                // إعادة تفعيل الأزرار
                OkButton.IsEnabled = true;
                OkButton.Content = "💾 حفظ";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
