using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Archif.Models;
using Archif.Services;
using Archif.Helpers;
using Button = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// نافذة عرض وثائق الضبارة
    /// </summary>
    public partial class FolderDocumentsWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly int _folderId;
        private readonly string _folderName;
        private List<FolderDocumentViewModel> _allDocuments = new();

        public FolderDocumentsWindow(DatabaseService databaseService, int folderId, string folderName)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _folderId = folderId;
            _folderName = folderName;

            Loaded += FolderDocumentsWindow_Loaded;
        }

        private async void FolderDocumentsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            FolderNameText.Text = $"وثائق ضبارة: {_folderName}";
            await LoadDocumentsAsync();
        }

        private async Task LoadDocumentsAsync()
        {
            try
            {
                var documents = await _databaseService.GetDocumentsAsync();
                var folderDocuments = documents.Where(d => d.FolderId == _folderId).ToList();

                _allDocuments.Clear();

                foreach (var doc in folderDocuments)
                {
                    var attachmentsCount = await _databaseService.GetAttachmentsByDocumentIdAsync(doc.Id);

                    _allDocuments.Add(new FolderDocumentViewModel
                    {
                        Id = doc.Id,
                        DocumentNumber = doc.DocumentNumber,
                        Subject = doc.Subject,
                        DocumentDate = doc.DocumentDate,
                        Type = doc.Type,
                        FromTo = doc.FromTo,
                        AttachmentsCount = attachmentsCount.Count,
                        CreatedDate = doc.CreatedDate
                    });
                }

                ApplyFilter();
                UpdateDocumentsCount();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تحميل الوثائق");
            }
        }

        private void ApplyFilter()
        {
            try
            {
                var searchText = SearchTextBox?.Text?.ToLower() ?? string.Empty;

                var filteredDocuments = _allDocuments.Where(d =>
                    string.IsNullOrEmpty(searchText) ||
                    d.Subject.ToLower().Contains(searchText) ||
                    d.DocumentNumber.ToLower().Contains(searchText) ||
                    d.FromTo.ToLower().Contains(searchText)
                ).OrderByDescending(d => d.DocumentDate).ToList();

                DocumentsItemsControl.ItemsSource = filteredDocuments;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تطبيق الفلتر");
            }
        }

        private void UpdateDocumentsCount()
        {
            var count = _allDocuments.Count;
            DocumentsCountText.Text = $"{count} وثيقة";
        }

        private async void AddDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folder = await _databaseService.GetFolderByIdAsync(_folderId);
                if (folder != null)
                {
                    var addDocumentWindow = new AddDocumentWindow(_databaseService);
                    addDocumentWindow.Owner = this;

                    // ربط حدث إضافة الوثيقة
                    addDocumentWindow.DocumentAdded += async (s, args) =>
                    {
                        await LoadDocumentsAsync();
                    };

                    addDocumentWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "إضافة الوثيقة");
            }
        }

        private async void ViewDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is FolderDocumentViewModel docVM)
                {
                    var document = await _databaseService.GetDocumentByIdAsync(docVM.Id);
                    if (document != null)
                    {
                        var viewDocumentWindow = new ViewDocumentWindow(_databaseService, document);
                        viewDocumentWindow.Owner = this;
                        viewDocumentWindow.ShowDialog();

                        // تحديث البيانات بعد إغلاق النافذة
                        _ = LoadDocumentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "عرض الوثيقة");
            }
        }

        private async void EditDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is FolderDocumentViewModel docVM)
                {
                    var document = await _databaseService.GetDocumentByIdAsync(docVM.Id);
                    if (document != null)
                    {
                        var editDocumentWindow = new EditDocumentWindow(_databaseService, document);
                        editDocumentWindow.Owner = this;

                        if (editDocumentWindow.ShowDialog() == true)
                        {
                            _ = LoadDocumentsAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تعديل الوثيقة");
            }
        }

        private async void DeleteDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is FolderDocumentViewModel docVM)
                {
                    if (ErrorHandler.ShowConfirmation($"هل أنت متأكد من حذف الوثيقة '{docVM.Subject}'؟", "تأكيد الحذف"))
                    {
                        await _databaseService.DeleteDocumentAsync(docVM.Id);
                        await LoadDocumentsAsync();
                        ErrorHandler.ShowSuccess("تم حذف الوثيقة بنجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "حذف الوثيقة");
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // إخفاء/إظهار placeholder
            if (SearchPlaceholder != null)
            {
                SearchPlaceholder.Visibility = string.IsNullOrEmpty(SearchTextBox.Text)
                    ? Visibility.Visible
                    : Visibility.Hidden;
            }

            ApplyFilter();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    #region ViewModel

    public class FolderDocumentViewModel
    {
        public int Id { get; set; }
        public string DocumentNumber { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public DateTime DocumentDate { get; set; }
        public DocumentType Type { get; set; }
        public string FromTo { get; set; } = string.Empty;
        public int AttachmentsCount { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    #endregion
}
