using Microsoft.EntityFrameworkCore;
using Archif.Models;
using System.IO;

namespace Archif.Data
{
    public class ArchifDbContext : DbContext
    {
        public DbSet<Department> Departments { get; set; } = null!;
        public DbSet<Folder> Folders { get; set; } = null!;
        public DbSet<Document> Documents { get; set; } = null!;
        public DbSet<Attachment> Attachments { get; set; } = null!;
        public DbSet<DocumentHyperlink> DocumentHyperlinks { get; set; } = null!;
        public DbSet<Organization> Organizations { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                    "Archif", "archif.db");

            // إنشاء المجلد إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات
            modelBuilder.Entity<Document>()
                .HasOne(d => d.Department)
                .WithMany(dep => dep.Documents)
                .HasForeignKey(d => d.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Document>()
                .HasOne(d => d.Folder)
                .WithMany(f => f.Documents)
                .HasForeignKey(d => d.FolderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Folder>()
                .HasOne(f => f.Department)
                .WithMany(d => d.Folders)
                .HasForeignKey(f => f.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Attachment>()
                .HasOne(a => a.Document)
                .WithMany(d => d.Attachments)
                .HasForeignKey(a => a.DocumentId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DocumentHyperlink>()
                .HasOne(h => h.Document)
                .WithMany(d => d.Hyperlinks)
                .HasForeignKey(h => h.DocumentId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Document>()
                .HasOne(d => d.Organization)
                .WithMany(o => o.Documents)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.SetNull);

            // إضافة بيانات أولية
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إضافة أقسام افتراضية
            modelBuilder.Entity<Department>().HasData(
                new Department { Id = 1, Name = "الإدارة العامة", CreatedDate = DateTime.Now },
                new Department { Id = 2, Name = "الشؤون المالية", CreatedDate = DateTime.Now },
                new Department { Id = 3, Name = "الموارد البشرية", CreatedDate = DateTime.Now },
                new Department { Id = 4, Name = "الشؤون القانونية", CreatedDate = DateTime.Now }
            );

            // إضافة ضبائر افتراضية
            modelBuilder.Entity<Folder>().HasData(
                new Folder { Id = 1, Name = "المراسلات العامة", DepartmentId = 1, CreatedDate = DateTime.Now },
                new Folder { Id = 2, Name = "القرارات الإدارية", DepartmentId = 1, CreatedDate = DateTime.Now },
                new Folder { Id = 3, Name = "الميزانية", DepartmentId = 2, CreatedDate = DateTime.Now },
                new Folder { Id = 4, Name = "المصروفات", DepartmentId = 2, CreatedDate = DateTime.Now },
                new Folder { Id = 5, Name = "التوظيف", DepartmentId = 3, CreatedDate = DateTime.Now },
                new Folder { Id = 6, Name = "العقود", DepartmentId = 4, CreatedDate = DateTime.Now }
            );

            // إضافة جهات افتراضية
            modelBuilder.Entity<Organization>().HasData(
                new Organization { Id = 1, Name = "وزارة الداخلية", Type = OrganizationType.Government, CreatedDate = DateTime.Now },
                new Organization { Id = 2, Name = "وزارة المالية", Type = OrganizationType.Government, CreatedDate = DateTime.Now },
                new Organization { Id = 3, Name = "وزارة التعليم", Type = OrganizationType.Government, CreatedDate = DateTime.Now },
                new Organization { Id = 4, Name = "جامعة الملك سعود", Type = OrganizationType.Academic, CreatedDate = DateTime.Now },
                new Organization { Id = 5, Name = "شركة أرامكو السعودية", Type = OrganizationType.Private, CreatedDate = DateTime.Now }
            );
        }
    }
}
