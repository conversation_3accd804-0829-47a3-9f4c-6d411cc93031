using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using Archif.Models;
using WpfButton = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// نافذة إدارة الروابط
    /// </summary>
    public partial class HyperlinksManagerWindow : Window
    {
        private readonly ObservableCollection<DocumentHyperlink> _hyperlinks;

        public ObservableCollection<DocumentHyperlink> Hyperlinks => _hyperlinks;

        public HyperlinksManagerWindow(IEnumerable<DocumentHyperlink>? existingHyperlinks = null)
        {
            InitializeComponent();

            _hyperlinks = new ObservableCollection<DocumentHyperlink>();

            if (existingHyperlinks != null)
            {
                foreach (var hyperlink in existingHyperlinks)
                {
                    _hyperlinks.Add(hyperlink);
                }
            }

            HyperlinksDataGrid.ItemsSource = _hyperlinks;
        }

        private void AddHyperlinkButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateHyperlinkForm())
                    return;

                var hyperlink = new DocumentHyperlink
                {
                    Title = TitleTextBox.Text.Trim(),
                    Url = UrlTextBox.Text.Trim(),
                    Description = DescriptionTextBox.Text.Trim(),
                    CreatedDate = DateTime.Now
                };

                _hyperlinks.Add(hyperlink);
                ClearHyperlinkForm();

                System.Windows.MessageBox.Show("تم إضافة الرابط بنجاح", "نجح",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في إضافة الرابط: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateHyperlinkForm()
        {
            if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال عنوان الرابط", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TitleTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(UrlTextBox.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال الرابط (URL)", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                UrlTextBox.Focus();
                return false;
            }

            // التحقق من صحة الرابط
            if (!Uri.TryCreate(UrlTextBox.Text.Trim(), UriKind.Absolute, out _))
            {
                System.Windows.MessageBox.Show("يرجى إدخال رابط صحيح (مثال: https://www.example.com)", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                UrlTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearHyperlinkForm()
        {
            TitleTextBox.Clear();
            UrlTextBox.Clear();
            DescriptionTextBox.Clear();
        }

        private void OpenHyperlink_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is WpfButton button && button.Tag is DocumentHyperlink hyperlink)
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = hyperlink.Url,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في فتح الرابط: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteHyperlink_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is WpfButton button && button.Tag is DocumentHyperlink hyperlink)
                {
                    var result = System.Windows.MessageBox.Show($"هل أنت متأكد من حذف الرابط '{hyperlink.Title}'؟",
                                               "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _hyperlinks.Remove(hyperlink);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في حذف الرابط: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }
    }
}
