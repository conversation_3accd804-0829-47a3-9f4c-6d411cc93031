using System.Windows;
using System.Windows.Controls;
using Archif.Services;
using Archif.Models;
using WpfMessageBox = System.Windows.MessageBox;

namespace Archif.Views
{
    /// <summary>
    /// نافذة إضافة/تحرير الجهة
    /// </summary>
    public partial class AddEditOrganizationWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly Organization? _existingOrganization;
        private readonly bool _isEditMode;

        public AddEditOrganizationWindow(DatabaseService databaseService, Organization? organization = null)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _existingOrganization = organization;
            _isEditMode = organization != null;

            InitializeForm();
        }

        private void InitializeForm()
        {
            // تعيين العنوان
            TitleTextBlock.Text = _isEditMode ? "تحرير الجهة" : "إضافة جهة جديدة";
            SaveButton.Content = _isEditMode ? "حفظ التغييرات" : "إضافة الجهة";

            // تعيين القيم الافتراضية
            TypeComboBox.SelectedIndex = 0; // حكومية كافتراضي

            // إذا كان في وضع التحرير، املأ البيانات
            if (_isEditMode && _existingOrganization != null)
            {
                NameTextBox.Text = _existingOrganization.Name;
                DescriptionTextBox.Text = _existingOrganization.Description ?? string.Empty;

                // تعيين نوع الجهة
                for (int i = 0; i < TypeComboBox.Items.Count; i++)
                {
                    if (TypeComboBox.Items[i] is ComboBoxItem item &&
                        item.Tag?.ToString() == _existingOrganization.Type.ToString())
                    {
                        TypeComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            // التركيز على حقل الاسم
            NameTextBox.Focus();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!await ValidateForm())
                    return;

                var organization = _isEditMode ? _existingOrganization! : new Organization();

                organization.Name = NameTextBox.Text.Trim();
                organization.Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text)
                    ? null : DescriptionTextBox.Text.Trim();

                // تحديد نوع الجهة
                if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem &&
                    Enum.TryParse<OrganizationType>(selectedItem.Tag?.ToString(), out var organizationType))
                {
                    organization.Type = organizationType;
                }

                if (!_isEditMode)
                {
                    organization.CreatedDate = DateTime.Now;
                    organization.IsActive = true;
                }

                // حفظ في قاعدة البيانات
                if (_isEditMode)
                {
                    await _databaseService.UpdateOrganizationAsync(organization);
                    WpfMessageBox.Show("تم تحديث الجهة بنجاح", "نجح التحديث",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    await _databaseService.AddOrganizationAsync(organization);
                    WpfMessageBox.Show("تم إضافة الجهة بنجاح", "نجحت الإضافة",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في حفظ الجهة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task<bool> ValidateForm()
        {
            bool isValid = true;

            // إخفاء رسائل الخطأ السابقة
            NameErrorText.Visibility = Visibility.Collapsed;

            // التحقق من اسم الجهة
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                ShowFieldError(NameErrorText, "اسم الجهة مطلوب");
                isValid = false;
            }
            else if (NameTextBox.Text.Trim().Length < 2)
            {
                ShowFieldError(NameErrorText, "اسم الجهة يجب أن يكون أكثر من حرفين");
                isValid = false;
            }
            else
            {
                // التحقق من عدم تكرار الاسم
                var excludeId = _isEditMode ? _existingOrganization?.Id : null;
                var nameExists = await _databaseService.IsOrganizationNameExistsAsync(NameTextBox.Text.Trim(), excludeId);

                if (nameExists)
                {
                    ShowFieldError(NameErrorText, "اسم الجهة موجود بالفعل، يرجى اختيار اسم آخر");
                    isValid = false;
                }
            }

            // التحقق من نوع الجهة
            if (TypeComboBox.SelectedItem == null)
            {
                WpfMessageBox.Show("يرجى اختيار نوع الجهة", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeComboBox.Focus();
                isValid = false;
            }

            return isValid;
        }

        private void ShowFieldError(TextBlock errorTextBlock, string message)
        {
            errorTextBlock.Text = message;
            errorTextBlock.Visibility = Visibility.Visible;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (HasUnsavedChanges())
            {
                var result = WpfMessageBox.Show("هل أنت متأكد من إلغاء العملية؟ سيتم فقدان جميع التغييرات غير المحفوظة.",
                                           "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            DialogResult = false;
            Close();
        }

        private bool HasUnsavedChanges()
        {
            if (!_isEditMode)
            {
                return !string.IsNullOrWhiteSpace(NameTextBox.Text) ||
                       !string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ||
                       TypeComboBox.SelectedIndex != 0;
            }
            else if (_existingOrganization != null)
            {
                return NameTextBox.Text.Trim() != _existingOrganization.Name ||
                       DescriptionTextBox.Text.Trim() != (_existingOrganization.Description ?? string.Empty) ||
                       GetSelectedOrganizationType() != _existingOrganization.Type;
            }

            return false;
        }

        private OrganizationType GetSelectedOrganizationType()
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem &&
                Enum.TryParse<OrganizationType>(selectedItem.Tag?.ToString(), out var organizationType))
            {
                return organizationType;
            }
            return OrganizationType.Government;
        }
    }
}
