using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Diagnostics;
using Microsoft.Win32;
using Archif.Services;
using Archif.Helpers;
using WpfMessageBox = System.Windows.MessageBox;
using WpfOpenFileDialog = Microsoft.Win32.OpenFileDialog;

namespace Archif.Views
{
    /// <summary>
    /// صفحة الإعدادات
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.UserControl
    {
        private readonly DatabaseService _databaseService;

        public SettingsPage(DatabaseService databaseService)
        {
            InitializeComponent();
            _databaseService = databaseService;
            InitializeSettings();
        }

        private void InitializeSettings()
        {
            try
            {
                // تعيين مسار النسخ الاحتياطي الافتراضي
                var defaultBackupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                                                    "ArchifBackup");
                BackupPathTextBox.Text = defaultBackupPath;

                // عرض معلومات قاعدة البيانات
                var dbPath = _databaseService.GetDatabasePath();
                DatabaseInfoText.Text = $"مسار قاعدة البيانات: {dbPath}";
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تهيئة الإعدادات");
            }
        }

        private void BrowseBackupPathButton_Click(object sender, RoutedEventArgs e)
        {
            var folderDialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "اختر مجلد حفظ النسخة الاحتياطية"
            };

            if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                BackupPathTextBox.Text = folderDialog.SelectedPath;
            }
        }

        private async void CreateBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(BackupPathTextBox.Text))
                {
                    WpfMessageBox.Show("يرجى اختيار مجلد لحفظ النسخة الاحتياطية", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var backupFolder = BackupPathTextBox.Text;
                if (!Directory.Exists(backupFolder))
                {
                    Directory.CreateDirectory(backupFolder);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"Backup_{timestamp}.db";
                var backupPath = Path.Combine(backupFolder, backupFileName);

                // تعطيل الزر أثناء العملية
                CreateBackupButton.IsEnabled = false;
                CreateBackupButton.Content = "جاري الحفظ...";

                var success = await _databaseService.CreateBackupAsync(backupPath);

                if (success)
                {
                    WpfMessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح:\n{backupPath}", "نجح الحفظ",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    WpfMessageBox.Show("فشل في إنشاء النسخة الاحتياطية", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "إنشاء النسخة الاحتياطية");
            }
            finally
            {
                // إعادة تفعيل الزر
                CreateBackupButton.IsEnabled = true;
                CreateBackupButton.Content = "إنشاء نسخة احتياطية";
            }
        }

        private void BrowseRestorePathButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new WpfOpenFileDialog
            {
                Title = "اختر ملف النسخة الاحتياطية",
                Filter = "ملفات قاعدة البيانات|*.db|جميع الملفات|*.*",
                CheckFileExists = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                RestorePathTextBox.Text = openFileDialog.FileName;
            }
        }

        private async void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(RestorePathTextBox.Text))
                {
                    WpfMessageBox.Show("يرجى اختيار ملف النسخة الاحتياطية", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!File.Exists(RestorePathTextBox.Text))
                {
                    WpfMessageBox.Show("الملف المحدد غير موجود", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var result = WpfMessageBox.Show(
                    "تحذير: سيتم استبدال قاعدة البيانات الحالية بالكامل.\nهل أنت متأكد من المتابعة؟",
                    "تأكيد الاستعادة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    // تعطيل الزر أثناء العملية
                    RestoreButton.IsEnabled = false;
                    RestoreButton.Content = "جاري الاستعادة...";

                    var success = await _databaseService.RestoreBackupAsync(RestorePathTextBox.Text);

                    if (success)
                    {
                        WpfMessageBox.Show("تم استعادة البيانات بنجاح.\nيرجى إعادة تشغيل التطبيق لتطبيق التغييرات.",
                                      "نجحت الاستعادة",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        WpfMessageBox.Show("فشل في استعادة البيانات", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "استعادة البيانات");
            }
            finally
            {
                // إعادة تفعيل الزر
                RestoreButton.IsEnabled = true;
                RestoreButton.Content = "استعادة البيانات";
            }
        }

        /// <summary>
        /// فتح رابط الواتساب
        /// </summary>
        private void WhatsAppLink_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var whatsappNumber = "9647815883398";
                var whatsappUrl = $"https://wa.me/{whatsappNumber}";

                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "فتح الواتساب");

                // محاولة بديلة - نسخ الرقم إلى الحافظة
                try
                {
                    System.Windows.Clipboard.SetText("9647815883398");
                    WpfMessageBox.Show("تم نسخ رقم الواتساب إلى الحافظة: 9647815883398", "معلومات",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch
                {
                    WpfMessageBox.Show("رقم الواتساب: 9647815883398", "معلومات التواصل",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// فتح رابط البريد الإلكتروني
        /// </summary>
        private void EmailLink_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var emailAddress = "<EMAIL>";
                var subject = "استفسار حول نظام الأرشفة الإلكترونية";
                var body = "السلام عليكم ورحمة الله وبركاته،%0A%0Aأتواصل معكم بخصوص نظام الأرشفة الإلكترونية...";

                var mailtoUrl = $"mailto:{emailAddress}?subject={subject}&body={body}";

                Process.Start(new ProcessStartInfo
                {
                    FileName = mailtoUrl,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "فتح البريد الإلكتروني");

                // محاولة بديلة - نسخ البريد إلى الحافظة
                try
                {
                    System.Windows.Clipboard.SetText("<EMAIL>");
                    WpfMessageBox.Show("تم نسخ البريد الإلكتروني إلى الحافظة: <EMAIL>", "معلومات",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch
                {
                    WpfMessageBox.Show("البريد الإلكتروني: <EMAIL>", "معلومات التواصل",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }
}
