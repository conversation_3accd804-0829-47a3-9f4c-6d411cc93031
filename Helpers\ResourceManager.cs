using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Windows.Threading;

namespace Archif.Helpers
{
    /// <summary>
    /// مدير الموارد المحسن لمنع تسريب الذاكرة
    /// </summary>
    public class ResourceManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, IDisposable> _resources;
        private readonly ConcurrentDictionary<string, Timer> _timers;
        private readonly ConcurrentDictionary<string, DispatcherTimer> _dispatcherTimers;
        private bool _disposed = false;

        public ResourceManager()
        {
            _resources = new ConcurrentDictionary<string, IDisposable>();
            _timers = new ConcurrentDictionary<string, Timer>();
            _dispatcherTimers = new ConcurrentDictionary<string, DispatcherTimer>();
        }

        /// <summary>
        /// تسجيل مورد للتتبع والتنظيف التلقائي
        /// </summary>
        public void RegisterResource(string key, IDisposable resource)
        {
            if (_disposed) return;

            // التخلص من المورد السابق إذا كان موجوداً
            if (_resources.TryRemove(key, out var oldResource))
            {
                try
                {
                    oldResource?.Dispose();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"تنظيف المورد {key}");
                }
            }

            _resources.TryAdd(key, resource);
        }

        /// <summary>
        /// تسجيل مؤقت للتتبع والتنظيف التلقائي
        /// </summary>
        public void RegisterTimer(string key, Timer timer)
        {
            if (_disposed) return;

            // التخلص من المؤقت السابق إذا كان موجوداً
            if (_timers.TryRemove(key, out var oldTimer))
            {
                try
                {
                    oldTimer?.Dispose();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"تنظيف المؤقت {key}");
                }
            }

            _timers.TryAdd(key, timer);
        }

        /// <summary>
        /// تسجيل مؤقت Dispatcher للتتبع والتنظيف التلقائي
        /// </summary>
        public void RegisterDispatcherTimer(string key, DispatcherTimer timer)
        {
            if (_disposed) return;

            // إيقاف المؤقت السابق إذا كان موجوداً
            if (_dispatcherTimers.TryRemove(key, out var oldTimer))
            {
                try
                {
                    oldTimer?.Stop();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"إيقاف مؤقت Dispatcher {key}");
                }
            }

            _dispatcherTimers.TryAdd(key, timer);
        }

        /// <summary>
        /// إنشاء مؤقت مع التسجيل التلقائي
        /// </summary>
        public Timer CreateTimer(string key, TimerCallback callback, object state, TimeSpan dueTime, TimeSpan period)
        {
            var timer = new Timer(callback, state, dueTime, period);
            RegisterTimer(key, timer);
            return timer;
        }

        /// <summary>
        /// إنشاء مؤقت Dispatcher مع التسجيل التلقائي
        /// </summary>
        public DispatcherTimer CreateDispatcherTimer(string key, TimeSpan interval, EventHandler tickHandler)
        {
            var timer = new DispatcherTimer { Interval = interval };
            timer.Tick += tickHandler;
            RegisterDispatcherTimer(key, timer);
            return timer;
        }

        /// <summary>
        /// إلغاء تسجيل مورد والتخلص منه
        /// </summary>
        public void UnregisterResource(string key)
        {
            if (_resources.TryRemove(key, out var resource))
            {
                try
                {
                    resource?.Dispose();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"إلغاء تسجيل المورد {key}");
                }
            }
        }

        /// <summary>
        /// إلغاء تسجيل مؤقت والتخلص منه
        /// </summary>
        public void UnregisterTimer(string key)
        {
            if (_timers.TryRemove(key, out var timer))
            {
                try
                {
                    timer?.Dispose();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"إلغاء تسجيل المؤقت {key}");
                }
            }
        }

        /// <summary>
        /// إلغاء تسجيل مؤقت Dispatcher وإيقافه
        /// </summary>
        public void UnregisterDispatcherTimer(string key)
        {
            if (_dispatcherTimers.TryRemove(key, out var timer))
            {
                try
                {
                    timer?.Stop();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"إلغاء تسجيل مؤقت Dispatcher {key}");
                }
            }
        }

        /// <summary>
        /// الحصول على عدد الموارد المسجلة
        /// </summary>
        public int GetResourceCount()
        {
            return _resources.Count + _timers.Count + _dispatcherTimers.Count;
        }

        /// <summary>
        /// تنظيف جميع الموارد
        /// </summary>
        public void ClearAll()
        {
            // تنظيف الموارد العادية
            foreach (var kvp in _resources)
            {
                try
                {
                    kvp.Value?.Dispose();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"تنظيف المورد {kvp.Key}");
                }
            }
            _resources.Clear();

            // تنظيف المؤقتات
            foreach (var kvp in _timers)
            {
                try
                {
                    kvp.Value?.Dispose();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"تنظيف المؤقت {kvp.Key}");
                }
            }
            _timers.Clear();

            // تنظيف مؤقتات Dispatcher
            foreach (var kvp in _dispatcherTimers)
            {
                try
                {
                    kvp.Value?.Stop();
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError(ex, $"إيقاف مؤقت Dispatcher {kvp.Key}");
                }
            }
            _dispatcherTimers.Clear();
        }

        /// <summary>
        /// التخلص من جميع الموارد
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                ClearAll();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// مدير الموارد العام للتطبيق
    /// </summary>
    public static class GlobalResourceManager
    {
        private static readonly Lazy<ResourceManager> _instance = new Lazy<ResourceManager>(() => new ResourceManager());
        
        public static ResourceManager Instance => _instance.Value;

        /// <summary>
        /// تنظيف الموارد عند إغلاق التطبيق
        /// </summary>
        public static void Cleanup()
        {
            if (_instance.IsValueCreated)
            {
                _instance.Value.Dispose();
            }
        }
    }
}
