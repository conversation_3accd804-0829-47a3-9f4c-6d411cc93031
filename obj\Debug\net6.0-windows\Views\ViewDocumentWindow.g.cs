﻿#pragma checksum "..\..\..\..\Views\ViewDocumentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "48E3EF15350A0BBD714ACA07E3E1F522786C8E73"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Archif.Views {
    
    
    /// <summary>
    /// ViewDocumentWindow
    /// </summary>
    public partial class ViewDocumentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 23 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitleText;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentNumberText;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentDateText;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentTypeText;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArchiveSequenceText;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DepartmentText;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FolderText;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OrganizationText;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubjectText;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateText;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AttachmentsCountText;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AttachmentsListBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditDocumentButton;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintDocumentButton;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\ViewDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Archif;V2.0.0.0;component/views/viewdocumentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ViewDocumentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DocumentNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DocumentDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.DocumentTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ArchiveSequenceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DepartmentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.FolderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.OrganizationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.SubjectText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CreatedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.AttachmentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.AttachmentsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 15:
            this.EditDocumentButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\ViewDocumentWindow.xaml"
            this.EditDocumentButton.Click += new System.Windows.RoutedEventHandler(this.EditDocumentButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PrintDocumentButton = ((System.Windows.Controls.Button)(target));
            
            #line 228 "..\..\..\..\Views\ViewDocumentWindow.xaml"
            this.PrintDocumentButton.Click += new System.Windows.RoutedEventHandler(this.PrintDocumentButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\Views\ViewDocumentWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 195 "..\..\..\..\Views\ViewDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 200 "..\..\..\..\Views\ViewDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenAttachment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

