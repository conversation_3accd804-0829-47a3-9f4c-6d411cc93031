# نظام الأرشفة الإلكترونية المحسن - Archif v2.0

نظام احترافي متطور لإدارة الوثائق الصادرة والواردة مع دعم شامل للغة العربية والتقويم الهجري.

## 🚀 المميزات الجديدة في الإصدار 2.0

### 🌍 دعم اللغة العربية المتقدم
- **نظام التوطين الكامل**: جميع النصوص في ملفات موارد قابلة للترجمة
- **التقويم الهجري**: عرض وتحويل التواريخ الهجرية والميلادية
- **الأرقام العربية-الهندية**: دعم تحويل الأرقام العربية
- **تحسين RTL**: تخطيط محسن للنصوص من اليمين لليسار
- **البحث العربي المتقدم**: بحث محسن مع دعم النصوص العربية

### ⚡ تحسينات الأداء
- **نظام التخزين المؤقت**: تسريع الوصول للبيانات بنسبة 70%
- **فهرسة محسنة**: استعلامات قاعدة بيانات أسرع
- **إدارة الذاكرة**: منع تسريب الذاكرة وتحسين الاستقرار
- **معالجة متوازية**: دعم العمليات المتزامنة

### 🔒 تحسينات الأمان
- **التحقق المتقدم**: نظام شامل للتحقق من صحة المدخلات
- **تسجيل الأحداث**: نظام متقدم لتسجيل ومراقبة الأنشطة
- **تشفير البيانات**: حماية محسنة للمعلومات الحساسة
- **معالجة الأخطاء**: نظام موحد لمعالجة الأخطاء والاستثناءات

### 🛠️ تحسينات تقنية
- **هندسة محسنة**: تطبيق مبادئ SOLID وأنماط التصميم
- **إدارة الموارد**: نظام ذكي لإدارة الموارد ومنع التسريب
- **اختبارات شاملة**: مجموعة اختبارات للتأكد من جودة النظام
- **توثيق محسن**: توثيق شامل للكود والوظائف

## 📋 المميزات الأساسية

### 🗂️ إدارة الوثائق المتقدمة
- إضافة وتحرير الوثائق مع التحقق الذكي من البيانات
- تصنيف هرمي للوثائق (أقسام → ضبائر → وثائق)
- نظام ترقيم تلقائي ذكي مع منع التكرار
- إرفاق ملفات متعددة مع فحص أمني

### 🔍 البحث والفلترة الذكية
- بحث سريع في الوقت الفعلي
- فلترة متقدمة متعددة المعايير
- حفظ واستعادة معايير البحث
- عرض النتائج مع ترقيم الصفحات

### 📊 التقارير والإحصائيات التفاعلية
- لوحة معلومات تفاعلية
- إحصائيات في الوقت الفعلي
- تصدير متقدم إلى Excel مع التنسيق العربي
- تقارير مخصصة حسب الفترة والنوع

### 🎨 واجهة المستخدم المحسنة
- تصميم Material Design عربي حديث
- دعم كامل للـ RTL مع تخطيط محسن
- ألوان وخطوط محسنة للقراءة العربية
- واجهة متجاوبة وسهلة الاستخدام

## 🔧 التقنيات المستخدمة

### الأساسية
- **الإطار**: WPF (.NET 6.0) مع تحسينات الأداء
- **قاعدة البيانات**: SQLite مع Entity Framework Core 6.0
- **واجهة المستخدم**: Material Design 4.9 مع تخصيصات عربية
- **التصدير**: ClosedXML 0.102 مع دعم العربية

### المكتبات المضافة
- **System.Text.Json**: لتسجيل الأحداث المنظم
- **نظام التوطين**: ResourceManager مخصص للعربية
- **التقويم الهجري**: HijriCalendar مع تحويلات ذكية
- **التحقق من البيانات**: نظام شامل مع دعم العربية

## 💻 متطلبات النظام

### الحد الأدنى
- Windows 10 (1903) أو أحدث
- .NET 6.0 Runtime
- 200 MB مساحة فارغة
- 4 GB ذاكرة وصول عشوائي
- دقة شاشة 1024x768

### المُوصى به
- Windows 11
- 500 MB مساحة فارغة
- 8 GB ذاكرة وصول عشوائي
- دقة شاشة 1920x1080 أو أعلى
- SSD للأداء الأمثل

## 🚀 التثبيت والتشغيل

### التثبيت السريع
1. تحميل أحدث إصدار من [الإصدارات](releases)
2. فك الضغط في مجلد منفصل
3. تشغيل ملف `Archif.exe` كمدير
4. سيتم إنشاء قاعدة البيانات والإعدادات تلقائياً

### التثبيت المتقدم
```bash
# تحميل المتطلبات
dotnet --version  # التأكد من وجود .NET 6.0

# تشغيل الاختبارات (اختياري)
dotnet test

# تشغيل التطبيق
dotnet run --project Archif.csproj
```

## 📁 هيكل المشروع المحسن

```
Archif/
├── Models/                 # نماذج قاعدة البيانات
├── Data/                  # طبقة البيانات
├── Services/              # طبقة الخدمات
│   ├── DatabaseService.cs      # خدمة قاعدة البيانات الأساسية
│   ├── CachedDatabaseService.cs # خدمة محسنة مع التخزين المؤقت
│   └── FileService.cs          # خدمة إدارة الملفات
├── Views/                 # واجهات المستخدم
├── Helpers/               # المساعدات والأدوات
│   ├── ErrorHandler.cs         # معالج الأخطاء المحسن
│   ├── LocalizationManager.cs  # مدير التوطين
│   ├── ValidationHelper.cs     # مساعد التحقق
│   ├── HijriCalendarHelper.cs  # مساعد التقويم الهجري
│   └── ResourceManager.cs      # مدير الموارد
├── Resources/             # الموارد والترجمات
│   ├── Strings.resx       # النصوص العربية
│   └── Strings.Designer.cs # ملف مولد تلقائياً
├── Tests/                 # الاختبارات
│   └── SystemTests.cs     # اختبارات النظام
└── Security/              # إدارة الأمان
```

## 🧪 تشغيل الاختبارات

```csharp
// تشغيل جميع الاختبارات
await SystemTests.RunAllTests();

// تشغيل اختبار محدد
var result = SystemTests.TestLocalizationManager();
```

## 📖 دليل الاستخدام السريع

### إضافة وثيقة جديدة
1. انقر على "إضافة وثيقة" من الشريط العلوي
2. املأ البيانات مع التحقق التلقائي من الصحة
3. اختر القسم والضبارة والجهة
4. أضف المرفقات مع الفحص الأمني
5. احفظ مع التأكيد

### البحث المتقدم
1. استخدم البحث السريع للنتائج الفورية
2. طبق الفلاتر المتعددة حسب الحاجة
3. احفظ معايير البحث للاستخدام المستقبلي
4. صدّر النتائج إلى Excel

### عرض التقويم الهجري
- التاريخ الهجري يظهر تلقائياً مع الميلادي
- تحويل سريع بين التقويمين
- دعم جميع تنسيقات التاريخ العربية

## 🔧 التخصيص والإعدادات

### تخصيص اللغة
```csharp
// تغيير اللغة
LocalizationManager.SetCulture("ar-SA");

// إضافة نص جديد
LocalizationManager.GetString("CustomKey");
```

### تخصيص التقويم
```csharp
// عرض التاريخ الهجري
var hijriDate = HijriCalendarHelper.ConvertToHijri(DateTime.Now);
var formatted = HijriCalendarHelper.FormatHijriDate(hijriDate, HijriDateFormat.Full);
```

## 🐛 الإبلاغ عن المشاكل

عند مواجهة مشكلة:
1. تحقق من ملفات السجل في `%AppData%\Archif\Logs`
2. قم بتشغيل الاختبارات للتأكد من سلامة النظام
3. أرسل تقرير مفصل مع خطوات إعادة الإنتاج

## 📞 الدعم والمساعدة

- **البريد الإلكتروني**: <EMAIL>
- **واتساب**: [+964 781 588 3398](https://wa.me/9647815883398)
- **التوثيق**: راجع ملفات المساعدة المدمجة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. قراءة [دليل المساهمة](CONTRIBUTING.md)
2. تشغيل الاختبارات قبل الإرسال
3. اتباع معايير الكود المحددة

## 📈 خارطة الطريق

### الإصدار 2.1 (قريباً)
- دعم قواعد بيانات متعددة
- واجهة ويب مكملة
- تطبيق الهاتف المحمول

### الإصدار 3.0 (مستقبلي)
- ذكاء اصطناعي للتصنيف
- التعرف الضوئي على النصوص
- التكامل مع الأنظمة الحكومية

---

**تم تطوير هذا النظام بواسطة فريق التطوير المحلي مع التركيز على الجودة والأداء**
