﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media;
using Archif.Views;
using Archif.Services;

namespace Archif
{
    /// <summary>
    /// النافذة الرئيسية لنظام الأرشفة الإلكترونية
    /// </summary>
    public partial class MainWindow : Window
    {
        private DatabaseService? _databaseService;
        private DispatcherTimer? _timer;
        private bool _isInitialized = false;

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                // تهيئة غير متزامنة للمكونات
                Loaded += MainWindow_Loaded;
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تهيئة النافذة الرئيسية", ex);
                Close();
            }
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض رسالة تحميل
                ShowLoadingMessage();

                // تهيئة خدمة قاعدة البيانات
                await InitializeDatabaseServiceAsync();

                // تهيئة مؤقت التاريخ والوقت
                InitializeTimer();

                // تحديث التاريخ والوقت فوراً
                UpdateDateTime();

                // تحميل الصفحة الرئيسية
                LoadHomePage();

                // إخفاء رسالة التحميل
                HideLoadingMessage();

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في تهيئة التطبيق", ex);
                Close();
            }
        }

        private async Task InitializeDatabaseServiceAsync()
        {
            try
            {
                _databaseService = new DatabaseService();

                // انتظار تهيئة قاعدة البيانات بشكل كامل
                await _databaseService.InitializeDatabaseAsync();

                // إضافة بيانات تجريبية للاختبار
                var testDataSeeder = new TestDataSeeder(_databaseService, new FileService());
                await testDataSeeder.SeedTestDataAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("فشل في تهيئة قاعدة البيانات", ex);
            }
        }

        private void InitializeTimer()
        {
            try
            {
                _timer = new DispatcherTimer();
                _timer.Interval = TimeSpan.FromSeconds(1);
                _timer.Tick += Timer_Tick;
                _timer.Start();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("فشل في تهيئة مؤقت التاريخ والوقت", ex);
            }
        }

        private void ShowLoadingMessage()
        {
            try
            {
                if (MainContentControl != null)
                {
                    var loadingPanel = new StackPanel
                    {
                        HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                        VerticalAlignment = System.Windows.VerticalAlignment.Center
                    };

                    var loadingText = new TextBlock
                    {
                        Text = "جاري تحميل التطبيق...",
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x4A, 0x90, 0xE2)),
                        HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20)
                    };

                    var progressBar = new System.Windows.Controls.ProgressBar
                    {
                        Width = 300,
                        Height = 20,
                        IsIndeterminate = true
                    };

                    loadingPanel.Children.Add(loadingText);
                    loadingPanel.Children.Add(progressBar);

                    MainContentControl.Content = loadingPanel;
                }
            }
            catch
            {
                // في حالة فشل عرض رسالة التحميل، لا نوقف التطبيق
            }
        }

        private void HideLoadingMessage()
        {
            // سيتم إخفاء رسالة التحميل عند تحميل الصفحة الرئيسية
        }

        private void ShowErrorMessage(string title, Exception ex)
        {
            var message = $"{title}: {ex.Message}";
            if (ex.InnerException != null)
            {
                message += $"\n\nالسبب: {ex.InnerException.Message}";
            }
            message += $"\n\nتفاصيل:\n{ex.StackTrace}";

            System.Windows.MessageBox.Show(message, "خطأ في التطبيق",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            UpdateDateTime();
        }

        private void UpdateDateTime()
        {
            try
            {
                if (CurrentDateTime != null)
                {
                    var now = DateTime.Now;
                    CurrentDateTime.Text = $"{now:dddd، dd MMMM yyyy - HH:mm:ss}";
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل تحديث التاريخ، لا نوقف التطبيق
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التاريخ: {ex.Message}");
            }
        }

        private void NavigationListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // التأكد من أن التطبيق تم تهيئته بالكامل
                if (!_isInitialized || NavigationListBox.SelectedItem == null) return;

                var selectedItem = NavigationListBox.SelectedItem as ListBoxItem;

                if (selectedItem == HomeItem)
                {
                    LoadHomePage();
                    UpdatePageHeader("الرئيسية");
                }
                else if (selectedItem == DepartmentsItem)
                {
                    LoadDepartmentsPage();
                    UpdatePageHeader("الأقسام");
                }
                else if (selectedItem == OrganizationsItem)
                {
                    LoadOrganizationsPage();
                    UpdatePageHeader("الجهات");
                }
                else if (selectedItem == SettingsItem)
                {
                    LoadSettingsPage();
                    UpdatePageHeader("الإعدادات");
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في التنقل", ex);
            }
        }

        private void UpdatePageHeader(string title)
        {
            try
            {
                if (PageTitle != null)
                {
                    PageTitle.Text = title;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عنوان الصفحة: {ex.Message}");
            }
        }

        private void LoadHomePage()
        {
            try
            {
                if (_databaseService == null)
                {
                    System.Windows.MessageBox.Show("خدمة قاعدة البيانات غير متاحة", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var homePage = new HomePage(_databaseService);
                MainContentControl.Content = homePage;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل الصفحة الرئيسية: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDepartmentsPage()
        {
            try
            {
                if (_databaseService == null)
                {
                    System.Windows.MessageBox.Show("خدمة قاعدة البيانات غير متاحة", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var departmentsPage = new DepartmentsPage(_databaseService);
                MainContentControl.Content = departmentsPage;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل صفحة الأقسام: {ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void LoadOrganizationsPage()
        {
            try
            {
                if (_databaseService == null)
                {
                    System.Windows.MessageBox.Show("خدمة قاعدة البيانات غير متاحة", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var organizationsPage = new OrganizationsPage(_databaseService);
                MainContentControl.Content = organizationsPage;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل صفحة الجهات: {ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSettingsPage()
        {
            try
            {
                if (_databaseService == null)
                {
                    System.Windows.MessageBox.Show("خدمة قاعدة البيانات غير متاحة", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var settingsPage = new SettingsPage(_databaseService);
                MainContentControl.Content = settingsPage;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل صفحة الإعدادات: {ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            _databaseService?.Dispose();
            base.OnClosed(e);
        }
    }
}