using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Archif.Services;
using Archif.Models;
using Archif.Helpers;
using WpfButton = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// صفحة إدارة الأقسام والضبائر
    /// </summary>
    public partial class DepartmentsPage : System.Windows.Controls.UserControl
    {
        private readonly DatabaseService _databaseService = null!;
        private ObservableCollection<DepartmentViewModel> _allDepartments = null!;

        public DepartmentsPage(DatabaseService databaseService)
        {
            try
            {
                InitializeComponent();
                _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
                _allDepartments = new ObservableCollection<DepartmentViewModel>();

                // تعيين الفلتر الافتراضي
                StatusFilterComboBox.SelectedIndex = 0;

                // تحميل البيانات بعد تهيئة الواجهة
                Loaded += DepartmentsPage_Loaded;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تهيئة صفحة الأقسام");
            }
        }

        private async void DepartmentsPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // التحقق من صحة خدمة قاعدة البيانات
                if (_databaseService == null)
                {
                    ErrorHandler.ShowError("خدمة قاعدة البيانات غير متاحة");
                    return;
                }

                // تحميل الأقسام أولاً
                await LoadDepartments();

                // تحميل الإحصائيات
                await LoadStatistics();

                // تطبيق الفلاتر فقط إذا كانت البيانات محملة
                if (_allDepartments?.Any() == true)
                {
                    ApplyFilters();
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowDetailedError(ex, "تحميل البيانات");

                // في حالة الفشل، عرض رسالة للمستخدم مع خيار إعادة المحاولة
                if (ErrorHandler.ShowRetryOption("فشل في تحميل البيانات"))
                {
                    await LoadDataAsync();
                }
            }
        }

        private void LoadData()
        {
            _ = LoadDataAsync();
        }

        private async Task LoadDepartments()
        {
            try
            {
                // استخدام الطريقة المحسنة لتحميل الأقسام مع الإحصائيات
                var departmentsWithStats = await _databaseService.GetDepartmentsWithStatsAsync();
                _allDepartments.Clear();

                if (departmentsWithStats != null && departmentsWithStats.Any())
                {
                    foreach (var dept in departmentsWithStats)
                    {
                        _allDepartments.Add(new DepartmentViewModel
                        {
                            Id = dept.Id,
                            Name = dept.Name,
                            CreatedDate = dept.CreatedDate,
                            IsActive = dept.IsActive,
                            FoldersCount = dept.FoldersCount,
                            DocumentsCount = dept.DocumentsCount
                        });
                    }
                }
                else
                {
                    // إضافة بيانات تجريبية إذا لم توجد أقسام
                    await CreateSampleDataIfNeeded();
                }

                // ربط البيانات مباشرة بالواجهة
                DepartmentsItemsControl.ItemsSource = _allDepartments;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowDetailedError(ex, "تحميل الأقسام");
            }
        }

        private async Task LoadStatistics()
        {
            try
            {
                var totalDepartments = await _databaseService.GetDepartmentsCountAsync();
                var totalDocuments = await _databaseService.GetDocumentsCountAsync();

                TotalDepartmentsText.Text = totalDepartments.ToString();
                TotalDocumentsText.Text = totalDocuments.ToString();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تحميل الإحصائيات");
            }
        }

        private async void AddDepartmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new AddDepartmentWindow(_databaseService);
                if (dialog.ShowDialog() == true)
                {
                    await LoadDepartments();
                    await LoadStatistics(); // تحديث الإحصائيات
                    ErrorHandler.ShowSuccess("تم إضافة القسم بنجاح");
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "إضافة القسم");
            }
        }

        // تم إزالة وظيفة إضافة الضبائر من هذه الصفحة - ستكون متاحة في نافذة تفاصيل القسم

        private async void EditDepartmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is System.Windows.Controls.Button button && button.Tag is DepartmentViewModel departmentVM)
                {
                    var department = await _databaseService.GetDepartmentByIdAsync(departmentVM.Id);
                    if (department != null)
                    {
                        var dialog = new AddDepartmentWindow(_databaseService, department);
                        if (dialog.ShowDialog() == true)
                        {
                            await LoadDepartments();
                            await LoadStatistics();
                            ErrorHandler.ShowSuccess("تم تعديل القسم بنجاح");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تعديل القسم");
            }
        }

        private async void DeleteDepartmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is System.Windows.Controls.Button button && button.Tag is DepartmentViewModel departmentVM)
                {
                    // التحقق من إمكانية الحذف
                    if (!await CheckDepartmentCanBeDeleted(departmentVM.Id))
                        return;

                    if (ErrorHandler.ShowConfirmation($"هل أنت متأكد من حذف القسم '{departmentVM.Name}'؟", "تأكيد الحذف"))
                    {
                        await _databaseService.DeleteDepartmentAsync(departmentVM.Id);
                        await LoadDepartments();
                        await LoadStatistics();
                        ErrorHandler.ShowSuccess("تم حذف القسم بنجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "حذف القسم");
            }
        }

        private async void OpenDepartmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is System.Windows.Controls.Button button && button.Tag is DepartmentViewModel departmentVM)
                {
                    var departmentDetailsWindow = new DepartmentDetailsWindow(_databaseService, departmentVM.Id);
                    departmentDetailsWindow.Owner = Window.GetWindow(this);
                    departmentDetailsWindow.ShowDialog();

                    // تحديث البيانات بعد إغلاق النافذة
                    await LoadDepartments();
                    await LoadStatistics();
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "فتح تفاصيل القسم");
            }
        }

        #region البحث والفلترة

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // إخفاء/إظهار placeholder
            if (SearchPlaceholder != null)
            {
                SearchPlaceholder.Visibility = string.IsNullOrEmpty(SearchTextBox.Text)
                    ? Visibility.Visible
                    : Visibility.Hidden;
            }

            // التحقق من أن البيانات محملة قبل تطبيق الفلاتر
            if (_allDepartments != null && _allDepartments.Any())
            {
                ApplyFilters();
            }
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allDepartments != null && _allDepartments.Any())
            {
                ApplyFilters();
            }
        }

        private void ApplyFilters()
        {
            try
            {
                // التحقق من أن البيانات محملة
                if (_allDepartments == null || !_allDepartments.Any())
                {
                    if (DepartmentsItemsControl != null)
                        DepartmentsItemsControl.ItemsSource = null;
                    return;
                }

                var searchText = SearchTextBox?.Text?.ToLower() ?? string.Empty;
                var statusFilter = (StatusFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "All";

                // فلترة الأقسام
                var filteredDepartments = _allDepartments.Where(d =>
                    (string.IsNullOrEmpty(searchText) || d.Name.ToLower().Contains(searchText)) &&
                    (statusFilter == "All" ||
                     (statusFilter == "Active" && d.IsActive) ||
                     (statusFilter == "Inactive" && !d.IsActive))
                ).ToList();

                if (DepartmentsItemsControl != null)
                    DepartmentsItemsControl.ItemsSource = filteredDepartments;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تطبيق الفلاتر");
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        #endregion

        #region تحسين طرق الحذف مع التحقق

        private async Task<bool> CheckDepartmentCanBeDeleted(int departmentId)
        {
            try
            {
                var foldersCount = await _databaseService.GetFoldersByDepartmentCountAsync(departmentId);
                var documentsCount = await _databaseService.GetDocumentsByDepartmentCountAsync(departmentId);

                if (foldersCount > 0 || documentsCount > 0)
                {
                    var message = $"لا يمكن حذف هذا القسم لأنه يحتوي على:\n";
                    if (foldersCount > 0) message += $"• {foldersCount} ضبارة\n";
                    if (documentsCount > 0) message += $"• {documentsCount} وثيقة\n";
                    message += "\nيرجى حذف الضبارات والوثائق أولاً أو نقلها إلى قسم آخر.";

                    ErrorHandler.ShowWarning(message, "لا يمكن الحذف");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "التحقق من إمكانية الحذف");
                return false;
            }
        }

        #endregion

        #region إنشاء البيانات التجريبية

        private async Task CreateSampleDataIfNeeded()
        {
            try
            {
                // إنشاء أقسام تجريبية
                var sampleDepartmentNames = new[]
                {
                    "قسم الموارد البشرية",
                    "قسم المالية",
                    "قسم تقنية المعلومات",
                    "قسم الشؤون الإدارية"
                };

                foreach (var deptName in sampleDepartmentNames)
                {
                    await _databaseService.AddDepartmentAsync(deptName);
                }

                // إعادة تحميل الأقسام بعد إضافة البيانات التجريبية باستخدام الطريقة المحسنة
                var departmentsWithStats = await _databaseService.GetDepartmentsWithStatsAsync();
                _allDepartments.Clear();

                foreach (var dept in departmentsWithStats)
                {
                    _allDepartments.Add(new DepartmentViewModel
                    {
                        Id = dept.Id,
                        Name = dept.Name,
                        CreatedDate = dept.CreatedDate,
                        IsActive = dept.IsActive,
                        FoldersCount = dept.FoldersCount,
                        DocumentsCount = dept.DocumentsCount
                    });
                }

                // ربط البيانات بالواجهة
                DepartmentsItemsControl.ItemsSource = _allDepartments;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "إنشاء البيانات التجريبية");
            }
        }

        #endregion
    }

    #region ViewModels

    public class DepartmentViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
        public int FoldersCount { get; set; }
        public int DocumentsCount { get; set; }
    }

    #endregion
}
