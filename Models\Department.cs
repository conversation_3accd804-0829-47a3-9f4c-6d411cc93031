using System.ComponentModel.DataAnnotations;

namespace Archif.Models
{
    public class Department
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<Folder> Folders { get; set; } = new List<Folder>();
    }
}
