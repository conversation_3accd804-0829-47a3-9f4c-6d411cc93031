﻿#pragma checksum "..\..\..\..\Views\AddDocumentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FB2F4CB2B53E621E76184F73BA761742DB124FFE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Archif.Views {
    
    
    /// <summary>
    /// AddDocumentWindow
    /// </summary>
    public partial class AddDocumentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 53 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton OutgoingRadio;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton IncomingRadio;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SequenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker CreatedDatePicker;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DocumentNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DocumentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SubjectTextBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DepartmentComboBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FolderComboBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OrganizationComboBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ArchiveSequenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFilesButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanButton;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFromCameraButton;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AttachmentsListBox;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ScannerComboBox;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshScannersButton;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Views\AddDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Archif;V2.0.0.0;component/views/adddocumentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddDocumentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OutgoingRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 57 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.OutgoingRadio.Checked += new System.Windows.RoutedEventHandler(this.DocumentType_Changed);
            
            #line default
            #line hidden
            return;
            case 2:
            this.IncomingRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 60 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.IncomingRadio.Checked += new System.Windows.RoutedEventHandler(this.DocumentType_Changed);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SequenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CreatedDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.DocumentNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DocumentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.SubjectTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.DepartmentComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 117 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.DepartmentComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DepartmentComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.FolderComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.OrganizationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.ArchiveSequenceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 143 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.ArchiveSequenceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ArchiveSequenceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AddFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.AddFilesButton.Click += new System.Windows.RoutedEventHandler(this.AddFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ScanButton = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.ScanButton.Click += new System.Windows.RoutedEventHandler(this.ScanButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.AddFromCameraButton = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.AddFromCameraButton.Click += new System.Windows.RoutedEventHandler(this.AddFromCameraButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.AttachmentsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 19:
            this.ScannerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            this.RefreshScannersButton = ((System.Windows.Controls.Button)(target));
            
            #line 282 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.RefreshScannersButton.Click += new System.Windows.RoutedEventHandler(this.RefreshScannersButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 301 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 308 "..\..\..\..\Views\AddDocumentWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 249 "..\..\..\..\Views\AddDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 254 "..\..\..\..\Views\AddDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 259 "..\..\..\..\Views\AddDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveAttachment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

