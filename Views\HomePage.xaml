<UserControl x:Class="Archif.Views.HomePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI, Tahoma, Arial"
             Unloaded="UserControl_Unloaded">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- بطاقات الإحصائيات -->
            <Grid Grid.Row="0" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي الوثائق -->
                <Border Grid.Column="0" Margin="10" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="📄" FontSize="32" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock x:Name="TotalDocumentsText"
                                      Text="0"
                                      FontSize="36"
                                      FontWeight="Bold"
                                      Margin="5,0,0,0"
                                      VerticalAlignment="Center"
                                      Foreground="#4A90E2"/>
                        </StackPanel>
                        <TextBlock Text="إجمالي الوثائق"
                                  FontSize="16"
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"
                                  Foreground="#333333"/>
                    </StackPanel>
                </Border>

                <!-- الكتب الصادرة -->
                <Border Grid.Column="1" Margin="10" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="📤" FontSize="32" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock x:Name="OutgoingCountText"
                                      Text="0"
                                      FontSize="36"
                                      FontWeight="Bold"
                                      Margin="5,0,0,0"
                                      VerticalAlignment="Center"
                                      Foreground="#28A745"/>
                        </StackPanel>
                        <TextBlock Text="الكتب الصادرة"
                                  FontSize="16"
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"
                                  Foreground="#333333"/>
                    </StackPanel>
                </Border>

                <!-- الكتب الواردة -->
                <Border Grid.Column="2" Margin="10" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="📥" FontSize="32" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock x:Name="IncomingCountText"
                                      Text="0"
                                      FontSize="36"
                                      FontWeight="Bold"
                                      Margin="5,0,0,0"
                                      VerticalAlignment="Center"
                                      Foreground="#DC3545"/>
                        </StackPanel>
                        <TextBlock Text="الكتب الواردة"
                                  FontSize="16"
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"
                                  Foreground="#333333"/>
                    </StackPanel>
                </Border>

                <!-- هذا الشهر -->
                <Border Grid.Column="3" Margin="10" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="📅" FontSize="32" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock x:Name="ThisMonthCountText"
                                      Text="0"
                                      FontSize="36"
                                      FontWeight="Bold"
                                      Margin="5,0,0,0"
                                      VerticalAlignment="Center"
                                      Foreground="#FFC107"/>
                        </StackPanel>
                        <TextBlock Text="هذا الشهر"
                                  FontSize="16"
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"
                                  Foreground="#333333"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- شريط الأدوات -->
            <Border Grid.Row="1" Margin="0,0,0,20" Padding="20"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- أزرار العمليات -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Button x:Name="AddDocumentButton"
                               Background="#4A90E2"
                               Foreground="White"
                               BorderThickness="0"
                               Padding="15,10"
                               Click="AddDocumentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة وثيقة" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ExportExcelButton"
                               Background="White"
                               Foreground="#28A745"
                               BorderBrush="#28A745"
                               BorderThickness="1"
                               Padding="15,10"
                               Margin="10,0,0,0"
                               Click="ExportExcelButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📊" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير Excel" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ExportPdfButton"
                               Background="White"
                               Foreground="#DC3545"
                               BorderBrush="#DC3545"
                               BorderThickness="1"
                               Padding="15,10"
                               Margin="10,0,0,0"
                               Click="ExportPdfButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📄" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير PDF" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- البحث السريع -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal"
                               HorizontalAlignment="Center" Margin="20,0">
                        <TextBox x:Name="QuickSearchTextBox"
                                Width="300" Height="35" Padding="10,8"
                                BorderBrush="#E0E0E0" BorderThickness="1"
                                TextChanged="QuickSearchTextBox_TextChanged">
                            <TextBox.Style>
                                <Style TargetType="TextBox">
                                    <Style.Triggers>
                                        <Trigger Property="Text" Value="">
                                            <Setter Property="Background">
                                                <Setter.Value>
                                                    <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                        <VisualBrush.Visual>
                                                            <TextBlock Text="البحث السريع (رقم الكتاب، الموضوع، الجهة...)"
                                                                      Foreground="#999999" Margin="5,0"/>
                                                        </VisualBrush.Visual>
                                                    </VisualBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBox.Style>
                        </TextBox>
                    </StackPanel>

                    <!-- أزرار التحكم -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="AdvancedSearchButton"
                               Background="White" Foreground="#666666"
                               BorderBrush="#E0E0E0" BorderThickness="1"
                               Padding="15,10" Margin="5,0"
                               Click="AdvancedSearchButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="بحث متقدم" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="RefreshButton"
                               Background="White" Foreground="#666666"
                               BorderBrush="#E0E0E0" BorderThickness="1"
                               Padding="15,10" Margin="5,0"
                               Click="RefreshButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔄" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديث" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- فلاتر البحث المتقدم -->
            <Border Grid.Row="2" x:Name="AdvancedFiltersPanel" Margin="0,0,0,20" Padding="20"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8"
                   Visibility="Collapsed">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="🔍" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="فلاتر البحث المتقدم"
                                  FontSize="16" FontWeight="Bold"
                                  Margin="5,0,0,0" VerticalAlignment="Center"
                                  Foreground="#333333"/>
                    </StackPanel>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- الصف الأول -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="5">
                                <TextBlock Text="السنة" Margin="0,0,0,5" Foreground="#666666"/>
                                <ComboBox x:Name="YearFilterComboBox" Height="35" Padding="10,8"
                                         SelectionChanged="ComboBoxFilterChanged"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="5">
                                <TextBlock Text="القسم" Margin="0,0,0,5" Foreground="#666666"/>
                                <ComboBox x:Name="DepartmentFilterComboBox" Height="35" Padding="10,8"
                                         SelectionChanged="ComboBoxFilterChanged"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="5">
                                <TextBlock Text="الضبارة" Margin="0,0,0,5" Foreground="#666666"/>
                                <ComboBox x:Name="FolderFilterComboBox" Height="35" Padding="10,8"
                                         SelectionChanged="ComboBoxFilterChanged"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" Margin="5">
                                <TextBlock Text="الجهة" Margin="0,0,0,5" Foreground="#666666"/>
                                <ComboBox x:Name="OrganizationFilterComboBox" Height="35" Padding="10,8"
                                         SelectionChanged="ComboBoxFilterChanged"/>
                            </StackPanel>
                        </Grid>

                        <!-- الصف الثاني -->
                        <Grid Grid.Row="1" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="5">
                                <TextBlock Text="نوع الوثيقة" Margin="0,0,0,5" Foreground="#666666"/>
                                <ComboBox x:Name="TypeFilterComboBox" Height="35" Padding="10,8"
                                         SelectionChanged="ComboBoxFilterChanged">
                                    <ComboBoxItem Content="الكل" Tag="All"/>
                                    <ComboBoxItem Content="صادر" Tag="Outgoing"/>
                                    <ComboBoxItem Content="وارد" Tag="Incoming"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="5">
                                <TextBlock Text="من تاريخ" Margin="0,0,0,5" Foreground="#666666"/>
                                <DatePicker x:Name="FromDatePicker" Height="35" Padding="10,8"
                                           SelectedDateChanged="DateFilterChanged"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Margin="5">
                                <TextBlock Text="إلى تاريخ" Margin="0,0,0,5" Foreground="#666666"/>
                                <DatePicker x:Name="ToDatePicker" Height="35" Padding="10,8"
                                           SelectedDateChanged="DateFilterChanged"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" Margin="5">
                                <TextBlock Text="رقم الكتاب" Margin="0,0,0,5" Foreground="#666666"/>
                                <TextBox x:Name="DocumentNumberFilterTextBox" Height="35" Padding="10,8"
                                        BorderBrush="#E0E0E0" BorderThickness="1"
                                        TextChanged="TextFilterChanged"/>
                            </StackPanel>
                        </Grid>

                        <!-- أزرار التحكم -->
                        <Grid Grid.Row="2">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button x:Name="ApplyFiltersButton"
                                       Content="تطبيق الفلاتر"
                                       Background="#4A90E2"
                                       Foreground="White"
                                       BorderThickness="0"
                                       Padding="20,10"
                                       Margin="5"
                                       Click="ApplyFiltersButton_Click"/>

                                <Button x:Name="ClearFiltersButton"
                                       Content="مسح الفلاتر"
                                       Background="White"
                                       Foreground="#666666"
                                       BorderBrush="#E0E0E0"
                                       BorderThickness="1"
                                       Padding="20,10"
                                       Margin="5"
                                       Click="ClearFiltersButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- جدول الوثائق -->
            <Border Grid.Row="4" Padding="20"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان الجدول -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="📄" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="قائمة الوثائق" FontSize="16" FontWeight="Bold"
                                  Margin="5,0,0,0" VerticalAlignment="Center" Foreground="#333333"/>
                        <TextBlock x:Name="DocumentsCountText" Text="(0 وثيقة)"
                                  FontSize="14" Margin="10,0,0,0"
                                  VerticalAlignment="Center" Foreground="#666666"/>
                    </StackPanel>

                    <!-- الجدول -->
                    <DataGrid Grid.Row="1" x:Name="DocumentsDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Background="White"
                             AlternatingRowBackground="#F8F9FA"
                             RowHeight="40"
                             MouseDoubleClick="DocumentsDataGrid_MouseDoubleClick">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="م"
                                               Binding="{Binding RowNumber}"
                                               Width="50" CanUserResize="False">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="رقم الكتاب"
                                               Binding="{Binding DocumentNumber}"
                                               Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="التاريخ"
                                               Binding="{Binding DocumentDate, StringFormat=dd/MM/yyyy}"
                                               Width="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="الموضوع"
                                               Binding="{Binding Subject}"
                                               Width="*" MinWidth="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="TextAlignment" Value="Center"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="النوع"
                                               Binding="{Binding TypeText}"
                                               Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="القسم"
                                               Binding="{Binding Department.Name}"
                                               Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="الضبارة"
                                               Binding="{Binding Folder.Name}"
                                               Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="الجهة"
                                               Binding="{Binding Organization.Name}"
                                               Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="تسلسل الحفظ"
                                               Binding="{Binding ArchiveSequence}"
                                               Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="العمليات" Width="150" CanUserResize="False">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="👁️" ToolTip="عرض"
                                                   Background="#4A90E2" Foreground="White"
                                                   BorderThickness="0" Width="28" Height="28"
                                                   Margin="2"
                                                   Click="ViewDocumentButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                            <Button Content="📝" ToolTip="تحرير"
                                                   Background="#28A745" Foreground="White"
                                                   BorderThickness="0" Width="28" Height="28"
                                                   Margin="2"
                                                   Click="EditDocumentButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                            <Button Content="🗑️" ToolTip="حذف"
                                                   Background="#DC3545" Foreground="White"
                                                   BorderThickness="0" Width="28" Height="28"
                                                   Margin="2"
                                                   Click="DeleteDocumentButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#DC3545" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                            <Button Content="🖨️" ToolTip="طباعة"
                                                   Background="#FFC107" Foreground="White"
                                                   BorderThickness="0" Width="28" Height="28"
                                                   Margin="2"
                                                   Click="PrintDocumentButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#FFC107" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- شريط التصفح (Pagination) -->
            <Border Grid.Row="5" Margin="0,20,0,0" Padding="15"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- معلومات الصفحة -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="عرض" Foreground="#666666" Margin="0,0,5,0"/>
                        <ComboBox x:Name="PageSizeComboBox" Width="60" Height="30"
                                 SelectionChanged="PageSizeComboBox_SelectionChanged">
                            <ComboBoxItem Content="10" Tag="10"/>
                            <ComboBoxItem Content="25" Tag="25" IsSelected="True"/>
                            <ComboBoxItem Content="50" Tag="50"/>
                            <ComboBoxItem Content="100" Tag="100"/>
                        </ComboBox>
                        <TextBlock Text="من" Foreground="#666666" Margin="5,0"/>
                        <TextBlock x:Name="TotalRecordsText" Text="0" Foreground="#666666"/>
                        <TextBlock Text="وثيقة" Foreground="#666666" Margin="5,0,0,0"/>
                    </StackPanel>

                    <!-- أزرار التصفح -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="FirstPageButton" Content="الأولى"
                               Background="White" Foreground="#666666"
                               BorderBrush="#E0E0E0" BorderThickness="1"
                               Padding="10,5" Margin="2"
                               Click="FirstPageButton_Click"/>
                        <Button x:Name="PreviousPageButton" Content="السابقة"
                               Background="White" Foreground="#666666"
                               BorderBrush="#E0E0E0" BorderThickness="1"
                               Padding="10,5" Margin="2"
                               Click="PreviousPageButton_Click"/>
                        <TextBlock x:Name="PageInfoText" Text="صفحة 1 من 1"
                                  VerticalAlignment="Center" Margin="10,0"
                                  Foreground="#333333"/>
                        <Button x:Name="NextPageButton" Content="التالية"
                               Background="White" Foreground="#666666"
                               BorderBrush="#E0E0E0" BorderThickness="1"
                               Padding="10,5" Margin="2"
                               Click="NextPageButton_Click"/>
                        <Button x:Name="LastPageButton" Content="الأخيرة"
                               Background="White" Foreground="#666666"
                               BorderBrush="#E0E0E0" BorderThickness="1"
                               Padding="10,5" Margin="2"
                               Click="LastPageButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
