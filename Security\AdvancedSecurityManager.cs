using System.Security.Cryptography;
using System.Text;
using System.IO;
using Microsoft.Win32;
using System.DirectoryServices.AccountManagement;

namespace Archif.Security
{
    /// <summary>
    /// مدير الأمان المتقدم للنظام
    /// </summary>
    public class AdvancedSecurityManager
    {
        private readonly string _encryptionKey;
        private readonly string _securityLogPath;

        public AdvancedSecurityManager()
        {
            _encryptionKey = GetOrCreateEncryptionKey();
            _securityLogPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "Archif", "Security", "security.log");
            
            EnsureSecurityDirectoryExists();
        }

        #region User Authentication & Authorization

        /// <summary>
        /// نموذج المستخدم
        /// </summary>
        public class User
        {
            public int Id { get; set; }
            public string Username { get; set; } = "";
            public string PasswordHash { get; set; } = "";
            public string FullName { get; set; } = "";
            public string Email { get; set; } = "";
            public UserRole Role { get; set; }
            public bool IsActive { get; set; } = true;
            public DateTime CreatedDate { get; set; } = DateTime.Now;
            public DateTime LastLoginDate { get; set; }
            public List<string> Permissions { get; set; } = new();
        }

        public enum UserRole
        {
            SuperAdmin,
            Admin,
            Manager,
            User,
            ReadOnly
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        public string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var salt = GenerateSalt();
            var saltedPassword = password + salt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashedBytes) + ":" + salt;
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                var parts = hashedPassword.Split(':');
                if (parts.Length != 2) return false;

                var hash = parts[0];
                var salt = parts[1];

                using var sha256 = SHA256.Create();
                var saltedPassword = password + salt;
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                var newHash = Convert.ToBase64String(hashedBytes);

                return hash == newHash;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء salt عشوائي
        /// </summary>
        private string GenerateSalt()
        {
            var saltBytes = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(saltBytes);
            return Convert.ToBase64String(saltBytes);
        }

        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        public bool HasPermission(User user, string permission)
        {
            if (user.Role == UserRole.SuperAdmin)
                return true;

            return user.Permissions.Contains(permission) || 
                   GetRolePermissions(user.Role).Contains(permission);
        }

        /// <summary>
        /// الحصول على صلاحيات الدور
        /// </summary>
        private List<string> GetRolePermissions(UserRole role)
        {
            return role switch
            {
                UserRole.SuperAdmin => new List<string> { "*" }, // جميع الصلاحيات
                UserRole.Admin => new List<string> 
                { 
                    "documents.create", "documents.edit", "documents.delete", "documents.view",
                    "departments.create", "departments.edit", "departments.delete", "departments.view",
                    "folders.create", "folders.edit", "folders.delete", "folders.view",
                    "users.create", "users.edit", "users.view",
                    "reports.generate", "backup.create", "backup.restore"
                },
                UserRole.Manager => new List<string>
                {
                    "documents.create", "documents.edit", "documents.view",
                    "departments.view", "folders.create", "folders.edit", "folders.view",
                    "reports.generate"
                },
                UserRole.User => new List<string>
                {
                    "documents.create", "documents.edit", "documents.view",
                    "departments.view", "folders.view"
                },
                UserRole.ReadOnly => new List<string>
                {
                    "documents.view", "departments.view", "folders.view"
                },
                _ => new List<string>()
            };
        }

        #endregion

        #region Data Encryption

        /// <summary>
        /// تشفير البيانات الحساسة
        /// </summary>
        public string EncryptSensitiveData(string data)
        {
            if (string.IsNullOrEmpty(data))
                return data;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Convert.FromBase64String(_encryptionKey);
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
                using var swEncrypt = new StreamWriter(csEncrypt);

                swEncrypt.Write(data);
                swEncrypt.Close();

                var encrypted = msEncrypt.ToArray();
                var result = new byte[aes.IV.Length + encrypted.Length];
                Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);

                return Convert.ToBase64String(result);
            }
            catch
            {
                return data; // في حالة فشل التشفير، إرجاع البيانات كما هي
            }
        }

        /// <summary>
        /// فك تشفير البيانات الحساسة
        /// </summary>
        public string DecryptSensitiveData(string encryptedData)
        {
            if (string.IsNullOrEmpty(encryptedData))
                return encryptedData;

            try
            {
                var fullCipher = Convert.FromBase64String(encryptedData);

                using var aes = Aes.Create();
                aes.Key = Convert.FromBase64String(_encryptionKey);

                var iv = new byte[aes.IV.Length];
                var cipher = new byte[fullCipher.Length - iv.Length];

                Array.Copy(fullCipher, 0, iv, 0, iv.Length);
                Array.Copy(fullCipher, iv.Length, cipher, 0, cipher.Length);

                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(cipher);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);

                return srDecrypt.ReadToEnd();
            }
            catch
            {
                return encryptedData; // في حالة فشل فك التشفير، إرجاع البيانات كما هي
            }
        }

        /// <summary>
        /// الحصول على مفتاح التشفير أو إنشاؤه
        /// </summary>
        private string GetOrCreateEncryptionKey()
        {
            try
            {
                using var key = Registry.CurrentUser.CreateSubKey(@"SOFTWARE\Archif\Security");
                var existingKey = key.GetValue("EncryptionKey") as string;

                if (!string.IsNullOrEmpty(existingKey))
                    return existingKey;

                // إنشاء مفتاح جديد
                using var aes = Aes.Create();
                aes.GenerateKey();
                var newKey = Convert.ToBase64String(aes.Key);

                key.SetValue("EncryptionKey", newKey);
                return newKey;
            }
            catch
            {
                // مفتاح افتراضي في حالة فشل الوصول للسجل
                return "YourDefaultEncryptionKeyHere1234567890ABCDEF";
            }
        }

        #endregion

        #region Security Logging

        /// <summary>
        /// تسجيل الأحداث الأمنية
        /// </summary>
        public void LogSecurityEvent(SecurityEventType eventType, string description, string username = "")
        {
            try
            {
                var logEntry = new SecurityLogEntry
                {
                    Timestamp = DateTime.Now,
                    EventType = eventType,
                    Description = description,
                    Username = username,
                    IPAddress = GetLocalIPAddress(),
                    MachineName = Environment.MachineName
                };

                var logLine = $"{logEntry.Timestamp:yyyy-MM-dd HH:mm:ss}|{eventType}|{username}|{description}|{logEntry.IPAddress}|{logEntry.MachineName}";
                
                File.AppendAllText(_securityLogPath, logLine + Environment.NewLine);
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }
        }

        public enum SecurityEventType
        {
            Login,
            Logout,
            LoginFailed,
            PasswordChanged,
            UserCreated,
            UserDeleted,
            PermissionChanged,
            DataAccessed,
            DataModified,
            DataDeleted,
            BackupCreated,
            BackupRestored,
            SecurityViolation
        }

        public class SecurityLogEntry
        {
            public DateTime Timestamp { get; set; }
            public SecurityEventType EventType { get; set; }
            public string Description { get; set; } = "";
            public string Username { get; set; } = "";
            public string IPAddress { get; set; } = "";
            public string MachineName { get; set; } = "";
        }

        #endregion

        #region File Security

        /// <summary>
        /// تشفير ملف
        /// </summary>
        public bool EncryptFile(string filePath)
        {
            try
            {
                var fileBytes = File.ReadAllBytes(filePath);
                var encryptedBytes = EncryptBytes(fileBytes);
                File.WriteAllBytes(filePath + ".encrypted", encryptedBytes);
                
                LogSecurityEvent(SecurityEventType.DataModified, $"تم تشفير الملف: {filePath}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فك تشفير ملف
        /// </summary>
        public bool DecryptFile(string encryptedFilePath, string outputPath)
        {
            try
            {
                var encryptedBytes = File.ReadAllBytes(encryptedFilePath);
                var decryptedBytes = DecryptBytes(encryptedBytes);
                File.WriteAllBytes(outputPath, decryptedBytes);
                
                LogSecurityEvent(SecurityEventType.DataAccessed, $"تم فك تشفير الملف: {encryptedFilePath}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        private byte[] EncryptBytes(byte[] data)
        {
            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(_encryptionKey);
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            msEncrypt.Write(aes.IV, 0, aes.IV.Length);
            
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            csEncrypt.Write(data, 0, data.Length);
            csEncrypt.FlushFinalBlock();

            return msEncrypt.ToArray();
        }

        private byte[] DecryptBytes(byte[] encryptedData)
        {
            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(_encryptionKey);

            var iv = new byte[aes.IV.Length];
            Array.Copy(encryptedData, 0, iv, 0, iv.Length);
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(encryptedData, iv.Length, encryptedData.Length - iv.Length);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var msOutput = new MemoryStream();
            
            csDecrypt.CopyTo(msOutput);
            return msOutput.ToArray();
        }

        #endregion

        #region Helper Methods

        private void EnsureSecurityDirectoryExists()
        {
            var securityDir = Path.GetDirectoryName(_securityLogPath);
            if (!Directory.Exists(securityDir))
                Directory.CreateDirectory(securityDir);
        }

        private string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                return host.AddressList
                    .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    ?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        public PasswordStrength CheckPasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return PasswordStrength.VeryWeak;

            var score = 0;
            
            if (password.Length >= 8) score++;
            if (password.Length >= 12) score++;
            if (password.Any(char.IsUpper)) score++;
            if (password.Any(char.IsLower)) score++;
            if (password.Any(char.IsDigit)) score++;
            if (password.Any(c => !char.IsLetterOrDigit(c))) score++;

            return score switch
            {
                >= 6 => PasswordStrength.VeryStrong,
                >= 5 => PasswordStrength.Strong,
                >= 4 => PasswordStrength.Medium,
                >= 2 => PasswordStrength.Weak,
                _ => PasswordStrength.VeryWeak
            };
        }

        public enum PasswordStrength
        {
            VeryWeak,
            Weak,
            Medium,
            Strong,
            VeryStrong
        }

        #endregion
    }
}
