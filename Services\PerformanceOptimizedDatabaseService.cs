using Microsoft.EntityFrameworkCore;
using Archif.Data;
using Archif.Models;
using System.Collections.Concurrent;
using System.Linq.Expressions;

namespace Archif.Services
{
    /// <summary>
    /// خدمة قاعدة البيانات محسنة للأداء
    /// </summary>
    public class PerformanceOptimizedDatabaseService : IDisposable
    {
        private readonly ArchifDbContext _context;
        private readonly ConcurrentDictionary<string, object> _cache;
        private readonly Timer _cacheCleanupTimer;
        private readonly SemaphoreSlim _dbSemaphore;
        private bool _disposed = false;

        public PerformanceOptimizedDatabaseService()
        {
            _context = new ArchifDbContext();
            _cache = new ConcurrentDictionary<string, object>();
            _dbSemaphore = new SemaphoreSlim(5, 5); // حد أقصى 5 عمليات متزامنة
            
            // تنظيف الكاش كل 10 دقائق
            _cacheCleanupTimer = new Timer(CleanupCache, null, 
                TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
            
            ConfigureDatabase();
        }

        private void ConfigureDatabase()
        {
            // تحسين إعدادات قاعدة البيانات
            _context.Database.ExecuteSqlRaw("PRAGMA journal_mode=WAL");
            _context.Database.ExecuteSqlRaw("PRAGMA synchronous=NORMAL");
            _context.Database.ExecuteSqlRaw("PRAGMA cache_size=10000");
            _context.Database.ExecuteSqlRaw("PRAGMA temp_store=MEMORY");
            
            // إنشاء فهارس محسنة
            CreateOptimizedIndexes();
        }

        private void CreateOptimizedIndexes()
        {
            try
            {
                // فهارس للوثائق
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Documents_Search 
                    ON Documents(Subject, DocumentNumber, FromTo)");
                
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Documents_Date_Type 
                    ON Documents(DocumentDate, Type, IsDeleted)");
                
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Documents_Department_Folder 
                    ON Documents(DepartmentId, FolderId, IsDeleted)");
                
                // فهارس للمرفقات
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Attachments_Document 
                    ON Attachments(DocumentId, IsDeleted)");
                
                // فهارس للأقسام والضبائر
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Folders_Department 
                    ON Folders(DepartmentId, IsActive)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الفهارس: {ex.Message}");
            }
        }

        /// <summary>
        /// استعلام محسن للوثائق مع Pagination
        /// </summary>
        public async Task<(List<DocumentViewModel> Documents, int TotalCount)> GetDocumentsOptimizedAsync(
            int page, int pageSize, DocumentSearchCriteria criteria = null)
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                criteria ??= new DocumentSearchCriteria();
                var cacheKey = GenerateCacheKey("documents", page, pageSize, criteria);
                
                if (_cache.TryGetValue(cacheKey, out var cachedResult))
                {
                    return ((List<DocumentViewModel>, int))cachedResult;
                }

                var query = BuildOptimizedDocumentQuery(criteria);
                
                // استعلام العدد الإجمالي
                var totalCount = await query.CountAsync();
                
                // استعلام البيانات مع Projection محسن
                var documents = await query
                    .OrderByDescending(d => d.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(d => new DocumentViewModel
                    {
                        Id = d.Id,
                        DocumentNumber = d.DocumentNumber,
                        DocumentDate = d.DocumentDate,
                        Subject = d.Subject,
                        TypeText = d.Type == DocumentType.Incoming ? "وارد" : "صادر",
                        DepartmentName = d.Department.Name,
                        FolderName = d.Folder.Name,
                        OrganizationName = d.Organization != null ? d.Organization.Name : "",
                        ArchiveSequence = d.ArchiveSequence,
                        FromTo = d.FromTo
                    })
                    .ToListAsync();

                // إضافة أرقام الصفوف
                for (int i = 0; i < documents.Count; i++)
                {
                    documents[i].RowNumber = ((page - 1) * pageSize) + i + 1;
                }

                var result = (documents, totalCount);
                
                // حفظ في الكاش لمدة 5 دقائق
                _cache.TryAdd(cacheKey, result);
                
                return result;
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        private IQueryable<Document> BuildOptimizedDocumentQuery(DocumentSearchCriteria criteria)
        {
            var query = _context.Documents
                .Include(d => d.Department)
                .Include(d => d.Folder)
                .Include(d => d.Organization)
                .Where(d => !d.IsDeleted)
                .AsQueryable();

            // تطبيق الفلاتر بكفاءة
            if (!string.IsNullOrWhiteSpace(criteria.SearchText))
            {
                var searchLower = criteria.SearchText.ToLower();
                query = query.Where(d => 
                    d.Subject.ToLower().Contains(searchLower) ||
                    d.DocumentNumber.ToLower().Contains(searchLower) ||
                    d.FromTo.ToLower().Contains(searchLower));
            }

            if (criteria.DepartmentId.HasValue)
                query = query.Where(d => d.DepartmentId == criteria.DepartmentId.Value);

            if (criteria.FolderId.HasValue)
                query = query.Where(d => d.FolderId == criteria.FolderId.Value);

            if (criteria.OrganizationId.HasValue)
                query = query.Where(d => d.OrganizationId == criteria.OrganizationId.Value);

            if (criteria.DocumentType.HasValue)
                query = query.Where(d => d.Type == criteria.DocumentType.Value);

            if (criteria.FromDate.HasValue)
                query = query.Where(d => d.DocumentDate >= criteria.FromDate.Value);

            if (criteria.ToDate.HasValue)
                query = query.Where(d => d.DocumentDate <= criteria.ToDate.Value);

            if (!string.IsNullOrWhiteSpace(criteria.DocumentNumber))
                query = query.Where(d => d.DocumentNumber.Contains(criteria.DocumentNumber));

            return query;
        }

        /// <summary>
        /// تحميل محسن للإحصائيات
        /// </summary>
        public async Task<DashboardStatistics> GetDashboardStatisticsAsync()
        {
            const string cacheKey = "dashboard_stats";
            
            if (_cache.TryGetValue(cacheKey, out var cachedStats))
            {
                return (DashboardStatistics)cachedStats;
            }

            await _dbSemaphore.WaitAsync();
            try
            {
                // استعلام واحد محسن للحصول على جميع الإحصائيات
                var stats = await _context.Documents
                    .Where(d => !d.IsDeleted)
                    .GroupBy(d => 1)
                    .Select(g => new DashboardStatistics
                    {
                        TotalDocuments = g.Count(),
                        IncomingDocuments = g.Count(d => d.Type == DocumentType.Incoming),
                        OutgoingDocuments = g.Count(d => d.Type == DocumentType.Outgoing),
                        ThisMonthDocuments = g.Count(d => d.CreatedDate.Month == DateTime.Now.Month 
                                                       && d.CreatedDate.Year == DateTime.Now.Year),
                        ThisYearDocuments = g.Count(d => d.CreatedDate.Year == DateTime.Now.Year)
                    })
                    .FirstOrDefaultAsync() ?? new DashboardStatistics();

                // إحصائيات إضافية
                stats.TotalDepartments = await _context.Departments.CountAsync(d => d.IsActive);
                stats.TotalFolders = await _context.Folders.CountAsync(f => f.IsActive);
                stats.TotalOrganizations = await _context.Organizations.CountAsync(o => o.IsActive);

                // حفظ في الكاش لمدة 10 دقائق
                _cache.TryAdd(cacheKey, stats);
                
                return stats;
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// تحميل محسن للأقسام مع عدد الوثائق
        /// </summary>
        public async Task<List<DepartmentWithStats>> GetDepartmentsWithStatsAsync()
        {
            const string cacheKey = "departments_with_stats";
            
            if (_cache.TryGetValue(cacheKey, out var cachedDepts))
            {
                return (List<DepartmentWithStats>)cachedDepts;
            }

            await _dbSemaphore.WaitAsync();
            try
            {
                var departments = await _context.Departments
                    .Where(d => d.IsActive)
                    .Select(d => new DepartmentWithStats
                    {
                        Id = d.Id,
                        Name = d.Name,
                        CreatedDate = d.CreatedDate,
                        IsActive = d.IsActive,
                        FoldersCount = d.Folders.Count(f => f.IsActive),
                        DocumentsCount = d.Documents.Count(doc => !doc.IsDeleted),
                        IncomingCount = d.Documents.Count(doc => !doc.IsDeleted && doc.Type == DocumentType.Incoming),
                        OutgoingCount = d.Documents.Count(doc => !doc.IsDeleted && doc.Type == DocumentType.Outgoing)
                    })
                    .OrderBy(d => d.Name)
                    .ToListAsync();

                _cache.TryAdd(cacheKey, departments);
                return departments;
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// بحث سريع محسن
        /// </summary>
        public async Task<List<QuickSearchResult>> QuickSearchAsync(string searchTerm, int maxResults = 10)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
                return new List<QuickSearchResult>();

            var cacheKey = $"quick_search_{searchTerm.ToLower()}_{maxResults}";
            
            if (_cache.TryGetValue(cacheKey, out var cachedResults))
            {
                return (List<QuickSearchResult>)cachedResults;
            }

            await _dbSemaphore.WaitAsync();
            try
            {
                var searchLower = searchTerm.ToLower();
                
                var results = await _context.Documents
                    .Where(d => !d.IsDeleted && (
                        d.Subject.ToLower().Contains(searchLower) ||
                        d.DocumentNumber.ToLower().Contains(searchLower) ||
                        d.FromTo.ToLower().Contains(searchLower)))
                    .Select(d => new QuickSearchResult
                    {
                        Id = d.Id,
                        Title = d.Subject,
                        DocumentNumber = d.DocumentNumber,
                        Type = d.Type == DocumentType.Incoming ? "وارد" : "صادر",
                        Date = d.DocumentDate,
                        DepartmentName = d.Department.Name
                    })
                    .Take(maxResults)
                    .ToListAsync();

                _cache.TryAdd(cacheKey, results);
                return results;
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        private string GenerateCacheKey(string prefix, params object[] parameters)
        {
            var key = prefix + "_" + string.Join("_", parameters.Select(p => p?.ToString() ?? "null"));
            return key.Length > 250 ? key.Substring(0, 250) : key;
        }

        private void CleanupCache(object state)
        {
            try
            {
                var keysToRemove = new List<string>();
                var cutoffTime = DateTime.Now.AddMinutes(-15);

                foreach (var key in _cache.Keys)
                {
                    // منطق تنظيف بسيط - يمكن تحسينه
                    if (key.Contains("_" + DateTime.Now.AddMinutes(-20).ToString("HHmm")))
                    {
                        keysToRemove.Add(key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    _cache.TryRemove(key, out _);
                }

                // تنظيف الذاكرة
                if (_cache.Count > 1000)
                {
                    _cache.Clear();
                }
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }

        public void InvalidateCache(string pattern = null)
        {
            if (string.IsNullOrEmpty(pattern))
            {
                _cache.Clear();
            }
            else
            {
                var keysToRemove = _cache.Keys.Where(k => k.Contains(pattern)).ToList();
                foreach (var key in keysToRemove)
                {
                    _cache.TryRemove(key, out _);
                }
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cacheCleanupTimer?.Dispose();
                _dbSemaphore?.Dispose();
                _context?.Dispose();
                _disposed = true;
            }
        }
    }

    // نماذج البيانات المحسنة
    public class DocumentSearchCriteria
    {
        public string SearchText { get; set; }
        public int? DepartmentId { get; set; }
        public int? FolderId { get; set; }
        public int? OrganizationId { get; set; }
        public DocumentType? DocumentType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string DocumentNumber { get; set; }
    }

    public class DocumentViewModel
    {
        public int Id { get; set; }
        public int RowNumber { get; set; }
        public string DocumentNumber { get; set; }
        public DateTime DocumentDate { get; set; }
        public string Subject { get; set; }
        public string TypeText { get; set; }
        public string DepartmentName { get; set; }
        public string FolderName { get; set; }
        public string OrganizationName { get; set; }
        public string ArchiveSequence { get; set; }
        public string FromTo { get; set; }
    }

    public class DashboardStatistics
    {
        public int TotalDocuments { get; set; }
        public int IncomingDocuments { get; set; }
        public int OutgoingDocuments { get; set; }
        public int ThisMonthDocuments { get; set; }
        public int ThisYearDocuments { get; set; }
        public int TotalDepartments { get; set; }
        public int TotalFolders { get; set; }
        public int TotalOrganizations { get; set; }
    }

    public class DepartmentWithStats : Department
    {
        public int FoldersCount { get; set; }
        public int DocumentsCount { get; set; }
        public int IncomingCount { get; set; }
        public int OutgoingCount { get; set; }
    }

    public class QuickSearchResult
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string DocumentNumber { get; set; }
        public string Type { get; set; }
        public DateTime Date { get; set; }
        public string DepartmentName { get; set; }
    }
}
