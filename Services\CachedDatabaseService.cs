using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Archif.Data;
using Archif.Models;
using Archif.Helpers;
using Timer = System.Threading.Timer;

namespace Archif.Services
{
    /// <summary>
    /// خدمة قاعدة البيانات المحسنة مع التخزين المؤقت
    /// </summary>
    public class CachedDatabaseService : IDisposable
    {
        private readonly ArchifDbContext _context;
        private readonly ConcurrentDictionary<string, (object Data, DateTime CachedAt)> _cache;
        private readonly SemaphoreSlim _dbSemaphore;
        private readonly Timer _cacheCleanupTimer;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(10);
        private bool _disposed = false;

        public CachedDatabaseService()
        {
            _context = new ArchifDbContext();
            _cache = new ConcurrentDictionary<string, (object, DateTime)>();
            _dbSemaphore = new SemaphoreSlim(5, 5); // حد أقصى 5 عمليات متزامنة
            
            // تنظيف الكاش كل 5 دقائق
            _cacheCleanupTimer = new Timer(CleanupCache, null, 
                TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            ConfigureDatabase();
        }

        /// <summary>
        /// تكوين قاعدة البيانات للأداء الأمثل
        /// </summary>
        private void ConfigureDatabase()
        {
            try
            {
                // تحسين إعدادات SQLite
                _context.Database.ExecuteSqlRaw("PRAGMA journal_mode=WAL");
                _context.Database.ExecuteSqlRaw("PRAGMA synchronous=NORMAL");
                _context.Database.ExecuteSqlRaw("PRAGMA cache_size=10000");
                _context.Database.ExecuteSqlRaw("PRAGMA temp_store=MEMORY");
                
                // إنشاء فهارس محسنة
                CreateOptimizedIndexes();
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تكوين قاعدة البيانات");
            }
        }

        /// <summary>
        /// إنشاء فهارس محسنة للأداء
        /// </summary>
        private void CreateOptimizedIndexes()
        {
            try
            {
                // فهارس للوثائق
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Documents_Search 
                    ON Documents(DocumentNumber, Subject, DocumentDate, Type, IsDeleted)");
                
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Documents_Department_Folder 
                    ON Documents(DepartmentId, FolderId, IsDeleted)");
                
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Documents_Date_Type 
                    ON Documents(DocumentDate, Type, IsDeleted)");
                
                // فهارس للأقسام والضبائر
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Folders_Department 
                    ON Folders(DepartmentId, IsActive)");
                
                // فهارس للمرفقات
                _context.Database.ExecuteSqlRaw(@"
                    CREATE INDEX IF NOT EXISTS IX_Attachments_Document 
                    ON Attachments(DocumentId, IsDeleted)");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "إنشاء الفهارس");
            }
        }

        /// <summary>
        /// الحصول على البيانات من الكاش أو قاعدة البيانات
        /// </summary>
        private async Task<T> GetCachedDataAsync<T>(string cacheKey, Func<Task<T>> dataProvider)
        {
            // التحقق من وجود البيانات في الكاش
            if (_cache.TryGetValue(cacheKey, out var cachedItem))
            {
                if (DateTime.Now - cachedItem.CachedAt < _cacheExpiry)
                {
                    return (T)cachedItem.Data;
                }
                else
                {
                    // إزالة البيانات المنتهية الصلاحية
                    _cache.TryRemove(cacheKey, out _);
                }
            }

            // جلب البيانات من قاعدة البيانات
            await _dbSemaphore.WaitAsync();
            try
            {
                var data = await dataProvider();
                
                // حفظ في الكاش
                _cache.TryAdd(cacheKey, (data, DateTime.Now));
                
                return data;
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// إبطال الكاش لمفتاح معين
        /// </summary>
        private void InvalidateCache(string pattern)
        {
            var keysToRemove = _cache.Keys.Where(k => k.Contains(pattern)).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// تنظيف الكاش من البيانات المنتهية الصلاحية
        /// </summary>
        private void CleanupCache(object state)
        {
            try
            {
                var expiredKeys = _cache
                    .Where(kvp => DateTime.Now - kvp.Value.CachedAt > _cacheExpiry)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _cache.TryRemove(key, out _);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنظيف الكاش");
            }
        }

        /// <summary>
        /// الحصول على الأقسام مع التخزين المؤقت
        /// </summary>
        public async Task<List<Department>> GetDepartmentsAsync()
        {
            return await GetCachedDataAsync("departments", async () =>
            {
                return await _context.Departments
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.Name)
                    .ToListAsync();
            });
        }

        /// <summary>
        /// الحصول على الضبائر حسب القسم مع التخزين المؤقت
        /// </summary>
        public async Task<List<Folder>> GetFoldersByDepartmentAsync(int departmentId)
        {
            return await GetCachedDataAsync($"folders_dept_{departmentId}", async () =>
            {
                return await _context.Folders
                    .Include(f => f.Department)
                    .Where(f => f.DepartmentId == departmentId && f.IsActive)
                    .OrderBy(f => f.Name)
                    .ToListAsync();
            });
        }

        /// <summary>
        /// الحصول على الجهات مع التخزين المؤقت
        /// </summary>
        public async Task<List<Organization>> GetOrganizationsAsync()
        {
            return await GetCachedDataAsync("organizations", async () =>
            {
                return await _context.Organizations
                    .Where(o => o.IsActive)
                    .OrderBy(o => o.Name)
                    .ToListAsync();
            });
        }

        /// <summary>
        /// الحصول على الوثائق مع Pagination محسن
        /// </summary>
        public async Task<(List<Document> Documents, int TotalCount)> GetDocumentsPagedAsync(
            int page, int pageSize, string searchText = null, int? departmentId = null, 
            int? folderId = null, int? organizationId = null, DocumentType? documentType = null,
            DateTime? fromDate = null, DateTime? toDate = null, string documentNumber = null)
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                var query = _context.Documents
                    .Include(d => d.Department)
                    .Include(d => d.Folder)
                    .Include(d => d.Organization)
                    .Where(d => !d.IsDeleted);

                // تطبيق الفلاتر
                if (!string.IsNullOrWhiteSpace(searchText))
                {
                    query = query.Where(d => 
                        d.DocumentNumber.Contains(searchText) ||
                        d.Subject.Contains(searchText) ||
                        d.FromTo.Contains(searchText));
                }

                if (departmentId.HasValue)
                    query = query.Where(d => d.DepartmentId == departmentId.Value);

                if (folderId.HasValue)
                    query = query.Where(d => d.FolderId == folderId.Value);

                if (organizationId.HasValue)
                    query = query.Where(d => d.OrganizationId == organizationId.Value);

                if (documentType.HasValue)
                    query = query.Where(d => d.Type == documentType.Value);

                if (fromDate.HasValue)
                    query = query.Where(d => d.DocumentDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(d => d.DocumentDate <= toDate.Value);

                if (!string.IsNullOrWhiteSpace(documentNumber))
                    query = query.Where(d => d.DocumentNumber.Contains(documentNumber));

                // حساب العدد الإجمالي
                var totalCount = await query.CountAsync();

                // جلب البيانات مع Pagination
                var documents = await query
                    .OrderByDescending(d => d.DocumentDate)
                    .ThenByDescending(d => d.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (documents, totalCount);
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// إضافة وثيقة جديدة مع إبطال الكاش
        /// </summary>
        public async Task<Document> AddDocumentAsync(Document document)
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                _context.Documents.Add(document);
                await _context.SaveChangesAsync();
                
                // إبطال الكاش المتعلق بالوثائق
                InvalidateCache("documents");
                InvalidateCache("stats");
                
                return document;
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// الحصول على إحصائيات محسنة
        /// </summary>
        public async Task<DocumentStatistics> GetStatisticsAsync()
        {
            return await GetCachedDataAsync("stats", async () =>
            {
                var stats = new DocumentStatistics();
                
                var documents = await _context.Documents
                    .Where(d => !d.IsDeleted)
                    .Select(d => new { d.Type, d.DocumentDate })
                    .ToListAsync();

                stats.TotalDocuments = documents.Count;
                stats.OutgoingDocuments = documents.Count(d => d.Type == DocumentType.Outgoing);
                stats.IncomingDocuments = documents.Count(d => d.Type == DocumentType.Incoming);
                
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                stats.ThisMonthDocuments = documents.Count(d => 
                    d.DocumentDate.Month == currentMonth && d.DocumentDate.Year == currentYear);

                return stats;
            });
        }

        /// <summary>
        /// تنظيف جميع الموارد
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _cacheCleanupTimer?.Dispose();
                _dbSemaphore?.Dispose();
                _context?.Dispose();
                _cache?.Clear();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// إحصائيات الوثائق
    /// </summary>
    public class DocumentStatistics
    {
        public int TotalDocuments { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
        public int ThisMonthDocuments { get; set; }
    }
}
