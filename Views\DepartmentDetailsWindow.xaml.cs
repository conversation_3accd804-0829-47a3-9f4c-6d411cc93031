using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Archif.Models;
using Archif.Services;
using Archif.Helpers;
using Button = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// نافذة تفاصيل القسم مع إدارة الضبائر
    /// </summary>
    public partial class DepartmentDetailsWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly int _departmentId;
        private Department? _department;
        private List<FolderDetailViewModel> _allFolders = new();

        public DepartmentDetailsWindow(DatabaseService databaseService, int departmentId)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _departmentId = departmentId;

            Loaded += DepartmentDetailsWindow_Loaded;
        }

        private async void DepartmentDetailsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDepartmentDetailsAsync();
        }

        private async Task LoadDepartmentDetailsAsync()
        {
            try
            {
                // تحميل بيانات القسم
                _department = await _databaseService.GetDepartmentByIdAsync(_departmentId);
                if (_department == null)
                {
                    ErrorHandler.ShowError("لم يتم العثور على القسم المطلوب");
                    Close();
                    return;
                }

                // عرض معلومات القسم
                DepartmentNameText.Text = _department.Name;
                CreatedDateText.Text = $"تاريخ الإنشاء: {_department.CreatedDate:dd/MM/yyyy}";

                // تحديث حالة القسم
                if (_department.IsActive)
                {
                    StatusBorder.Background = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(0x28, 0xA7, 0x45));
                    StatusText.Text = "نشط";
                }
                else
                {
                    StatusBorder.Background = new System.Windows.Media.SolidColorBrush(
                        System.Windows.Media.Color.FromRgb(0xDC, 0x35, 0x45));
                    StatusText.Text = "غير نشط";
                }

                // تحميل الإحصائيات والضبائر
                await LoadStatisticsAsync();
                await LoadFoldersAsync();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تحميل تفاصيل القسم");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var foldersCount = await _databaseService.GetFoldersByDepartmentCountAsync(_departmentId);
                var documentsCount = await _databaseService.GetDocumentsByDepartmentCountAsync(_departmentId);

                // إحصائيات الوثائق حسب النوع
                var documents = await _databaseService.GetDocumentsAsync();
                var departmentDocs = documents.Where(d => d.DepartmentId == _departmentId).ToList();
                var incomingCount = departmentDocs.Count(d => d.Type == DocumentType.Incoming);
                var outgoingCount = departmentDocs.Count(d => d.Type == DocumentType.Outgoing);

                FoldersCountText.Text = foldersCount.ToString();
                DocumentsCountText.Text = documentsCount.ToString();
                IncomingDocsText.Text = incomingCount.ToString();
                OutgoingDocsText.Text = outgoingCount.ToString();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تحميل الإحصائيات");
            }
        }

        private async Task LoadFoldersAsync()
        {
            try
            {
                var folders = await _databaseService.GetFoldersByDepartmentAsync(_departmentId);
                _allFolders.Clear();

                foreach (var folder in folders)
                {
                    var documentsCount = await _databaseService.GetDocumentsByFolderCountAsync(folder.Id);

                    _allFolders.Add(new FolderDetailViewModel
                    {
                        Id = folder.Id,
                        Name = folder.Name,
                        DepartmentId = folder.DepartmentId,
                        CreatedDate = folder.CreatedDate,
                        DocumentsCount = documentsCount,
                        IsActive = folder.IsActive
                    });
                }

                ApplyFoldersFilter();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تحميل الضبائر");
            }
        }

        private void ApplyFoldersFilter()
        {
            try
            {
                var searchText = SearchFoldersTextBox?.Text?.ToLower() ?? string.Empty;

                var filteredFolders = _allFolders.Where(f =>
                    string.IsNullOrEmpty(searchText) ||
                    f.Name.ToLower().Contains(searchText)
                ).ToList();

                FoldersItemsControl.ItemsSource = filteredFolders;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تطبيق الفلتر");
            }
        }

        private async void AddFolderButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_department == null) return;

                var departments = new List<Department> { _department };
                var dialog = new AddFolderDialog(departments, "", _departmentId);

                if (dialog.ShowDialog() == true)
                {
                    await _databaseService.AddFolderAsync(dialog.FolderName, dialog.SelectedDepartmentId);
                    await LoadStatisticsAsync();
                    await LoadFoldersAsync();
                    ErrorHandler.ShowSuccess("تم إضافة الضبارة بنجاح");
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "إضافة الضبارة");
            }
        }

        private async void EditFolderButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is FolderDetailViewModel folderVM)
                {
                    var folder = await _databaseService.GetFolderByIdAsync(folderVM.Id);
                    if (folder != null && _department != null)
                    {
                        var departments = new List<Department> { _department };
                        var dialog = new AddFolderDialog(departments, folder.Name, folder.DepartmentId);

                        if (dialog.ShowDialog() == true)
                        {
                            folder.Name = dialog.FolderName;
                            await _databaseService.UpdateFolderAsync(folder);
                            await LoadFoldersAsync();
                            ErrorHandler.ShowSuccess("تم تعديل الضبارة بنجاح");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تعديل الضبارة");
            }
        }

        private async void DeleteFolderButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is FolderDetailViewModel folderVM)
                {
                    // التحقق من وجود وثائق في الضبارة
                    var documentsCount = await _databaseService.GetDocumentsByFolderCountAsync(folderVM.Id);
                    if (documentsCount > 0)
                    {
                        ErrorHandler.ShowWarning($"لا يمكن حذف هذه الضبارة لأنها تحتوي على {documentsCount} وثيقة.\n" +
                                               "يرجى حذف الوثائق أولاً أو نقلها إلى ضبارة أخرى.", "لا يمكن الحذف");
                        return;
                    }

                    if (ErrorHandler.ShowConfirmation($"هل أنت متأكد من حذف الضبارة '{folderVM.Name}'؟", "تأكيد الحذف"))
                    {
                        await _databaseService.DeleteFolderAsync(folderVM.Id);
                        await LoadStatisticsAsync();
                        await LoadFoldersAsync();
                        ErrorHandler.ShowSuccess("تم حذف الضبارة بنجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "حذف الضبارة");
            }
        }

        private void ViewFolderDocumentsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is FolderDetailViewModel folderVM)
                {
                    // فتح نافذة عرض وثائق الضبارة
                    var documentsWindow = new FolderDocumentsWindow(_databaseService, folderVM.Id, folderVM.Name);
                    documentsWindow.Owner = this;
                    documentsWindow.ShowDialog();

                    // تحديث الإحصائيات بعد إغلاق النافذة
                    _ = LoadStatisticsAsync();
                    _ = LoadFoldersAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "فتح وثائق الضبارة");
            }
        }

        private void EditDepartmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_department != null)
                {
                    var dialog = new AddDepartmentWindow(_databaseService, _department);
                    if (dialog.ShowDialog() == true)
                    {
                        // تحديث البيانات
                        _ = LoadDepartmentDetailsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تعديل القسم");
            }
        }

        private void SearchFoldersTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // إخفاء/إظهار placeholder
            if (SearchFoldersPlaceholder != null)
            {
                SearchFoldersPlaceholder.Visibility = string.IsNullOrEmpty(SearchFoldersTextBox.Text)
                    ? Visibility.Visible
                    : Visibility.Hidden;
            }

            ApplyFoldersFilter();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    #region ViewModel

    public class FolderDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int DepartmentId { get; set; }
        public DateTime CreatedDate { get; set; }
        public int DocumentsCount { get; set; }
        public bool IsActive { get; set; }
    }

    #endregion
}
