﻿#pragma checksum "..\..\..\..\Views\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D361A50849A0CF94BB36B9D3A4AF47BDA966FD56"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Archif.Views {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 54 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseBackupPathButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateBackupButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RestorePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseRestorePathButton;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreButton;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DarkModeToggle;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatabaseInfoText;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WhatsAppLink;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailLink;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Archif;V2.0.0.0;component/views/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.BrowseBackupPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Views\SettingsPage.xaml"
            this.BrowseBackupPathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseBackupPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CreateBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Views\SettingsPage.xaml"
            this.CreateBackupButton.Click += new System.Windows.RoutedEventHandler(this.CreateBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RestorePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BrowseRestorePathButton = ((System.Windows.Controls.Button)(target));
            
            #line 149 "..\..\..\..\Views\SettingsPage.xaml"
            this.BrowseRestorePathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseRestorePathButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\Views\SettingsPage.xaml"
            this.RestoreButton.Click += new System.Windows.RoutedEventHandler(this.RestoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DarkModeToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.DatabaseInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.WhatsAppLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 298 "..\..\..\..\Views\SettingsPage.xaml"
            this.WhatsAppLink.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.WhatsAppLink_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.EmailLink = ((System.Windows.Controls.TextBlock)(target));
            
            #line 328 "..\..\..\..\Views\SettingsPage.xaml"
            this.EmailLink.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.EmailLink_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

