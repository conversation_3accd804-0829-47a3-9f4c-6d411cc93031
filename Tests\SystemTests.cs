using System;
using System.Threading.Tasks;
using Archif.Helpers;
using Archif.Services;

namespace Archif.Tests
{
    /// <summary>
    /// اختبارات النظام الأساسية
    /// </summary>
    public static class SystemTests
    {
        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static async Task<bool> RunAllTests()
        {
            try
            {
                Console.WriteLine("بدء اختبارات النظام...");
                
                var results = new bool[]
                {
                    TestErrorHandler(),
                    TestLocalizationManager(),
                    TestValidationHelper(),
                    TestHijriCalendarHelper(),
                    TestResourceManager(),
                    await TestCachedDatabaseService()
                };

                var passedTests = 0;
                var totalTests = results.Length;

                foreach (var result in results)
                {
                    if (result) passedTests++;
                }

                Console.WriteLine($"نتائج الاختبارات: {passedTests}/{totalTests} نجح");
                
                return passedTests == totalTests;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تشغيل الاختبارات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار معالج الأخطاء
        /// </summary>
        public static bool TestErrorHandler()
        {
            try
            {
                Console.WriteLine("اختبار معالج الأخطاء...");

                // اختبار التسجيل
                ErrorHandler.LogInfo("اختبار التسجيل", "SystemTests");
                ErrorHandler.LogWarning("اختبار التحذير", "SystemTests");
                ErrorHandler.LogError(new Exception("اختبار الخطأ"), "SystemTests");

                Console.WriteLine("✓ اختبار معالج الأخطاء نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار معالج الأخطاء فشل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار مدير التوطين
        /// </summary>
        public static bool TestLocalizationManager()
        {
            try
            {
                Console.WriteLine("اختبار مدير التوطين...");

                // اختبار تعيين الثقافة
                LocalizationManager.SetCulture("ar-SA");

                // اختبار الحصول على النصوص
                var appName = LocalizationManager.GetString("AppName");
                var actionAdd = LocalizationManager.GetString("Action_Add");

                // اختبار تنسيق التاريخ
                var formattedDate = LocalizationManager.FormatDate(DateTime.Now);
                var formattedNumber = LocalizationManager.FormatNumber(12345.67m);

                // اختبار تحويل الأرقام
                var arabicNumerals = LocalizationManager.ConvertToArabicNumerals("123456");
                var englishNumerals = LocalizationManager.ConvertToEnglishNumerals("١٢٣٤٥٦");

                Console.WriteLine($"اسم التطبيق: {appName}");
                Console.WriteLine($"إضافة: {actionAdd}");
                Console.WriteLine($"التاريخ: {formattedDate}");
                Console.WriteLine($"الرقم: {formattedNumber}");
                Console.WriteLine($"الأرقام العربية: {arabicNumerals}");
                Console.WriteLine($"الأرقام الإنجليزية: {englishNumerals}");

                Console.WriteLine("✓ اختبار مدير التوطين نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار مدير التوطين فشل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار مساعد التحقق
        /// </summary>
        public static bool TestValidationHelper()
        {
            try
            {
                Console.WriteLine("اختبار مساعد التحقق...");

                // اختبار التحقق من النص العربي
                var arabicValidation = ValidationHelper.ValidateArabicText("نص عربي تجريبي", "النص", true, 5, 100);
                Console.WriteLine($"التحقق من النص العربي: {(arabicValidation.IsValid ? "صحيح" : "خطأ")}");

                // اختبار التحقق من رقم الوثيقة
                var documentValidation = ValidationHelper.ValidateDocumentNumber("123/2024");
                Console.WriteLine($"التحقق من رقم الوثيقة: {(documentValidation.IsValid ? "صحيح" : "خطأ")}");

                // اختبار التحقق من التاريخ
                var dateValidation = ValidationHelper.ValidateDate(DateTime.Now, "التاريخ", true);
                Console.WriteLine($"التحقق من التاريخ: {(dateValidation.IsValid ? "صحيح" : "خطأ")}");

                // اختبار التحقق من البريد الإلكتروني
                var emailValidation = ValidationHelper.ValidateEmail("<EMAIL>");
                Console.WriteLine($"التحقق من البريد الإلكتروني: {(emailValidation.IsValid ? "صحيح" : "خطأ")}");

                // اختبار تنظيف المدخلات
                var sanitized = ValidationHelper.SanitizeInput("<script>alert('test')</script>نص آمن");
                Console.WriteLine($"النص المنظف: {sanitized}");

                Console.WriteLine("✓ اختبار مساعد التحقق نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار مساعد التحقق فشل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار مساعد التقويم الهجري
        /// </summary>
        public static bool TestHijriCalendarHelper()
        {
            try
            {
                Console.WriteLine("اختبار مساعد التقويم الهجري...");

                var now = DateTime.Now;

                // اختبار التحويل إلى هجري
                var hijriDate = HijriCalendarHelper.ConvertToHijri(now);
                Console.WriteLine($"التاريخ الهجري: {hijriDate}");

                // اختبار التنسيقات المختلفة
                var shortFormat = HijriCalendarHelper.FormatHijriDate(now, HijriDateFormat.Short);
                var mediumFormat = HijriCalendarHelper.FormatHijriDate(now, HijriDateFormat.Medium);
                var longFormat = HijriCalendarHelper.FormatHijriDate(now, HijriDateFormat.Long);
                var fullFormat = HijriCalendarHelper.FormatHijriDate(now, HijriDateFormat.Full);

                Console.WriteLine($"تنسيق قصير: {shortFormat}");
                Console.WriteLine($"تنسيق متوسط: {mediumFormat}");
                Console.WriteLine($"تنسيق طويل: {longFormat}");
                Console.WriteLine($"تنسيق كامل: {fullFormat}");

                // اختبار التحويل العكسي
                var gregorianDate = HijriCalendarHelper.ConvertToGregorian(hijriDate.Year, hijriDate.Month, hijriDate.Day);
                Console.WriteLine($"التحويل العكسي: {gregorianDate:yyyy/MM/dd}");

                // اختبار أسماء الأشهر والأيام
                var monthName = HijriCalendarHelper.GetHijriMonthName(hijriDate.Month);
                var dayName = HijriCalendarHelper.GetHijriDayName(hijriDate.DayOfWeek);
                Console.WriteLine($"اسم الشهر: {monthName}");
                Console.WriteLine($"اسم اليوم: {dayName}");

                Console.WriteLine("✓ اختبار مساعد التقويم الهجري نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار مساعد التقويم الهجري فشل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار مدير الموارد
        /// </summary>
        public static bool TestResourceManager()
        {
            try
            {
                Console.WriteLine("اختبار مدير الموارد...");

                using var resourceManager = new ResourceManager();

                // اختبار تسجيل مؤقت
                var timer = resourceManager.CreateTimer("TestTimer", _ => { }, null, 
                    TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

                // اختبار عدد الموارد
                var resourceCount = resourceManager.GetResourceCount();
                Console.WriteLine($"عدد الموارد المسجلة: {resourceCount}");

                // اختبار إلغاء التسجيل
                resourceManager.UnregisterTimer("TestTimer");

                var newResourceCount = resourceManager.GetResourceCount();
                Console.WriteLine($"عدد الموارد بعد الإلغاء: {newResourceCount}");

                Console.WriteLine("✓ اختبار مدير الموارد نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار مدير الموارد فشل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار خدمة قاعدة البيانات المحسنة
        /// </summary>
        public static async Task<bool> TestCachedDatabaseService()
        {
            try
            {
                Console.WriteLine("اختبار خدمة قاعدة البيانات المحسنة...");

                using var service = new CachedDatabaseService();

                // اختبار الحصول على الأقسام
                var departments = await service.GetDepartmentsAsync();
                Console.WriteLine($"عدد الأقسام: {departments.Count}");

                // اختبار الحصول على الجهات
                var organizations = await service.GetOrganizationsAsync();
                Console.WriteLine($"عدد الجهات: {organizations.Count}");

                // اختبار الحصول على الإحصائيات
                var stats = await service.GetStatisticsAsync();
                Console.WriteLine($"إجمالي الوثائق: {stats.TotalDocuments}");
                Console.WriteLine($"الوثائق الصادرة: {stats.OutgoingDocuments}");
                Console.WriteLine($"الوثائق الواردة: {stats.IncomingDocuments}");

                Console.WriteLine("✓ اختبار خدمة قاعدة البيانات المحسنة نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار خدمة قاعدة البيانات المحسنة فشل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الأداء
        /// </summary>
        public static async Task<bool> TestPerformance()
        {
            try
            {
                Console.WriteLine("اختبار الأداء...");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                // اختبار سرعة التحويل الهجري
                for (int i = 0; i < 1000; i++)
                {
                    var date = DateTime.Now.AddDays(i);
                    var hijriDate = HijriCalendarHelper.ConvertToHijri(date);
                    var formatted = HijriCalendarHelper.FormatHijriDate(hijriDate);
                }

                stopwatch.Stop();
                Console.WriteLine($"وقت تحويل 1000 تاريخ هجري: {stopwatch.ElapsedMilliseconds} مللي ثانية");

                // اختبار سرعة التحقق من البيانات
                stopwatch.Restart();

                for (int i = 0; i < 1000; i++)
                {
                    var validation = ValidationHelper.ValidateArabicText($"نص تجريبي {i}", "النص", true, 5, 100);
                }

                stopwatch.Stop();
                Console.WriteLine($"وقت التحقق من 1000 نص: {stopwatch.ElapsedMilliseconds} مللي ثانية");

                Console.WriteLine("✓ اختبار الأداء نجح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ اختبار الأداء فشل: {ex.Message}");
                return false;
            }
        }
    }
}
