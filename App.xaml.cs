﻿using System;
using System.Windows;
using Archif.Helpers;

namespace Archif
{
    /// <summary>
    /// تطبيق الأرشفة الإلكترونية المحسن
    /// </summary>
    public partial class App : System.Windows.Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);

                // تهيئة نظام التوطين
                LocalizationManager.SetCulture("ar-SA");

                // تهيئة معالج الأخطاء العام
                DispatcherUnhandledException += Application_DispatcherUnhandledException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                // تسجيل بداية التطبيق
                ErrorHandler.LogInfo("بدء تشغيل التطبيق", "App.OnStartup");

                // تشغيل النافذة الرئيسية
                var mainWindow = new MainWindow();
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "بدء التطبيق");
                Shutdown(1);
            }
        }

        private void Application_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                ErrorHandler.ShowError(e.Exception, "خطأ غير متوقع في التطبيق");
                e.Handled = true;
            }
            catch
            {
                // في حالة فشل معالج الأخطاء نفسه
                System.Windows.MessageBox.Show($"خطأ حرج: {e.Exception.Message}",
                              "خطأ في التطبيق",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
                e.Handled = true;
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception ex)
                {
                    ErrorHandler.ShowError(ex, "خطأ حرج في النطاق");
                }
            }
            catch
            {
                // في حالة فشل معالج الأخطاء نفسه
                System.Windows.MessageBox.Show("خطأ حرج غير معروف في التطبيق",
                              "خطأ حرج",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // تنظيف الموارد عند إغلاق التطبيق
                GlobalResourceManager.Cleanup();
                ErrorHandler.LogInfo("إغلاق التطبيق", "App.OnExit");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "إغلاق التطبيق");
            }
            finally
            {
                base.OnExit(e);
            }
        }
    }
}
