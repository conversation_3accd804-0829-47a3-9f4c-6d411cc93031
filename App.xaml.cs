﻿using System.Windows;

namespace Archif
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : System.Windows.Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                              "خطأ في التطبيق",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
                Shutdown();
            }
        }

        private void Application_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            System.Windows.MessageBox.Show($"خطأ غير متوقع: {e.Exception.Message}\n\nتفاصيل الخطأ:\n{e.Exception.StackTrace}",
                          "خطأ في التطبيق",
                          MessageBoxButton.OK,
                          MessageBoxImage.Error);
            e.Handled = true;
        }
    }
}
