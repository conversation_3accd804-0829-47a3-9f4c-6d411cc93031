using System;
using System.IO;
using System.Threading.Tasks;
using Archif.Models;

namespace Archif.Services
{
    /// <summary>
    /// خدمة إضافة بيانات تجريبية للاختبار
    /// </summary>
    public class TestDataSeeder
    {
        private readonly DatabaseService _databaseService;
        private readonly FileService _fileService;

        public TestDataSeeder(DatabaseService databaseService, FileService fileService)
        {
            _databaseService = databaseService;
            _fileService = fileService;
        }

        public async Task SeedTestDataAsync()
        {
            try
            {
                // التحقق من وجود وثائق
                var documentsCount = await _databaseService.GetDocumentsCountAsync();
                if (documentsCount > 0)
                {
                    return; // البيانات موجودة بالفعل
                }

                // إنشاء وثائق تجريبية
                await CreateTestDocuments();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة البيانات التجريبية: {ex.Message}");
            }
        }

        private async Task CreateTestDocuments()
        {
            // وثيقة صادرة
            var outgoingDocument = new Document
            {
                DocumentNumber = "2025/001",
                DocumentDate = DateTime.Now.AddDays(-5),
                Subject = "طلب موافقة على مشروع تطوير نظام الأرشفة الإلكترونية",
                Type = DocumentType.Outgoing,
                FromTo = "وزارة التقنية والاتصالات",
                DepartmentId = 1, // الإدارة العامة
                FolderId = 1, // المراسلات العامة
                OrganizationId = 1, // وزارة الداخلية
                Notes = "مشروع مهم لتطوير البنية التحتية الرقمية",
                CreatedDate = DateTime.Now.AddDays(-5)
            };

            var savedOutgoing = await _databaseService.AddDocumentAsync(outgoingDocument);

            // وثيقة واردة
            var incomingDocument = new Document
            {
                DocumentNumber = "2025/002",
                DocumentDate = DateTime.Now.AddDays(-3),
                Subject = "رد على طلب الموافقة على مشروع تطوير نظام الأرشفة",
                Type = DocumentType.Incoming,
                FromTo = "وزارة التقنية والاتصالات",
                DepartmentId = 1, // الإدارة العامة
                FolderId = 1, // المراسلات العامة
                OrganizationId = 1, // وزارة الداخلية
                Notes = "تمت الموافقة على المشروع مع بعض التعديلات",
                CreatedDate = DateTime.Now.AddDays(-3)
            };

            var savedIncoming = await _databaseService.AddDocumentAsync(incomingDocument);

            // وثيقة مالية
            var financialDocument = new Document
            {
                DocumentNumber = "2025/003",
                DocumentDate = DateTime.Now.AddDays(-1),
                Subject = "تقرير الميزانية الشهرية لشهر ديسمبر 2024",
                Type = DocumentType.Outgoing,
                FromTo = "وزارة المالية",
                DepartmentId = 2, // الشؤون المالية
                FolderId = 3, // الميزانية
                OrganizationId = 2, // وزارة المالية
                Notes = "تقرير شامل عن الوضع المالي للمؤسسة",
                CreatedDate = DateTime.Now.AddDays(-1)
            };

            var savedFinancial = await _databaseService.AddDocumentAsync(financialDocument);

            // إضافة مرفقات تجريبية
            await CreateTestAttachments(savedOutgoing.Id);
            await CreateTestAttachments(savedIncoming.Id);
            await CreateTestAttachments(savedFinancial.Id);
        }

        private async Task CreateTestAttachments(int documentId)
        {
            try
            {
                // إنشاء مجلد المرفقات إذا لم يكن موجوداً
                var attachmentsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                                  "Archif", "Attachments");
                Directory.CreateDirectory(attachmentsPath);

                // إنشاء ملف PDF تجريبي
                var pdfPath = Path.Combine(attachmentsPath, $"test_document_{documentId}.pdf");
                await CreateTestPdfFile(pdfPath);

                var pdfAttachment = new Attachment
                {
                    DocumentId = documentId,
                    FileName = $"test_document_{documentId}.pdf",
                    FilePath = pdfPath,
                    FileType = ".pdf",
                    MimeType = "application/pdf",
                    FileSize = new FileInfo(pdfPath).Length,
                    Type = AttachmentType.ScannedPdf,
                    Description = "ملف PDF تجريبي للاختبار",
                    CreatedDate = DateTime.Now
                };

                await _databaseService.AddAttachmentAsync(pdfAttachment);

                // إنشاء ملف نصي تجريبي
                var txtPath = Path.Combine(attachmentsPath, $"notes_{documentId}.txt");
                await CreateTestTextFile(txtPath, documentId);

                var txtAttachment = new Attachment
                {
                    DocumentId = documentId,
                    FileName = $"notes_{documentId}.txt",
                    FilePath = txtPath,
                    FileType = ".txt",
                    MimeType = "text/plain",
                    FileSize = new FileInfo(txtPath).Length,
                    Type = AttachmentType.Document,
                    Description = "ملاحظات إضافية حول الوثيقة",
                    CreatedDate = DateTime.Now
                };

                await _databaseService.AddAttachmentAsync(txtAttachment);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء المرفقات التجريبية: {ex.Message}");
            }
        }

        private async Task CreateTestPdfFile(string filePath)
        {
            try
            {
                // إنشاء ملف PDF بسيط (محاكاة)
                var pdfContent = @"%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF";

                await File.WriteAllTextAsync(filePath, pdfContent);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ملف PDF: {ex.Message}");
            }
        }

        private async Task CreateTestTextFile(string filePath, int documentId)
        {
            try
            {
                var content = $@"ملاحظات حول الوثيقة رقم {documentId}
==================================

تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm:ss}

هذا ملف تجريبي تم إنشاؤه لاختبار وظائف نظام الأرشفة الإلكترونية.

المحتويات:
- معلومات أساسية عن الوثيقة
- ملاحظات إضافية
- تفاصيل المتابعة

تم إنشاء هذا الملف تلقائياً بواسطة نظام الاختبار.
";

                await File.WriteAllTextAsync(filePath, content);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ملف نصي: {ex.Message}");
            }
        }
    }
}
