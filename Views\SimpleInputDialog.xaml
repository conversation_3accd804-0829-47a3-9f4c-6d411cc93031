<Window x:Class="Archif.Views.SimpleInputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding Title}" 
        Height="200" Width="400"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="#F5F5F5"
        ResizeMode="NoResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- المحتوى الرئيسي -->
        <Border Grid.Row="0" Margin="20" Padding="20" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel>
                <TextBlock x:Name="PromptTextBlock" 
                          Text="النص:" 
                          FontSize="14" 
                          Margin="0,0,0,10"
                          Foreground="#333333"/>
                
                <TextBox x:Name="InputTextBox" 
                        Height="35" 
                        Padding="10,8"
                        FontSize="14"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1"/>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <Border Grid.Row="1" 
               Background="White"
               BorderThickness="0,1,0,0"
               BorderBrush="#E0E0E0"
               Padding="20,15">
            
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center">
                <Button x:Name="OkButton"
                       Content="موافق"
                       Background="#4A90E2"
                       Foreground="White"
                       BorderThickness="0"
                       Width="100"
                       Height="35"
                       Margin="10,0"
                       Click="OkButton_Click"
                       IsDefault="True"/>
                
                <Button x:Name="CancelButton"
                       Content="إلغاء"
                       Background="White"
                       Foreground="#666666"
                       BorderBrush="#E0E0E0"
                       BorderThickness="1"
                       Width="100"
                       Height="35"
                       Margin="10,0"
                       Click="CancelButton_Click"
                       IsCancel="True"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
