﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Archif.Migrations
{
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Departments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Departments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Organizations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Organizations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Folders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Folders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Folders_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Documents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    SequenceNumber = table.Column<int>(type: "INTEGER", nullable: false),
                    DocumentNumber = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DocumentDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Subject = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    FromTo = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    FolderId = table.Column<int>(type: "INTEGER", nullable: false),
                    OrganizationId = table.Column<int>(type: "INTEGER", nullable: true),
                    ArchiveSequence = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Documents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Documents_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Documents_Folders_FolderId",
                        column: x => x.FolderId,
                        principalTable: "Folders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Documents_Organizations_OrganizationId",
                        column: x => x.OrganizationId,
                        principalTable: "Organizations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Attachments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DocumentId = table.Column<int>(type: "INTEGER", nullable: false),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    FileType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    MimeType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Attachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Attachments_Documents_DocumentId",
                        column: x => x.DocumentId,
                        principalTable: "Documents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DocumentHyperlinks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DocumentId = table.Column<int>(type: "INTEGER", nullable: false),
                    Title = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Url = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocumentHyperlinks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DocumentHyperlinks_Documents_DocumentId",
                        column: x => x.DocumentId,
                        principalTable: "Documents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Departments",
                columns: new[] { "Id", "CreatedDate", "IsActive", "Name" },
                values: new object[] { 1, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5097), true, "الإدارة العامة" });

            migrationBuilder.InsertData(
                table: "Departments",
                columns: new[] { "Id", "CreatedDate", "IsActive", "Name" },
                values: new object[] { 2, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5103), true, "الشؤون المالية" });

            migrationBuilder.InsertData(
                table: "Departments",
                columns: new[] { "Id", "CreatedDate", "IsActive", "Name" },
                values: new object[] { 3, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5108), true, "الموارد البشرية" });

            migrationBuilder.InsertData(
                table: "Departments",
                columns: new[] { "Id", "CreatedDate", "IsActive", "Name" },
                values: new object[] { 4, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5113), true, "الشؤون القانونية" });

            migrationBuilder.InsertData(
                table: "Organizations",
                columns: new[] { "Id", "CreatedDate", "Description", "IsActive", "Name", "Type" },
                values: new object[] { 1, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9707), null, true, "وزارة الداخلية", 0 });

            migrationBuilder.InsertData(
                table: "Organizations",
                columns: new[] { "Id", "CreatedDate", "Description", "IsActive", "Name", "Type" },
                values: new object[] { 2, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9869), null, true, "وزارة المالية", 0 });

            migrationBuilder.InsertData(
                table: "Organizations",
                columns: new[] { "Id", "CreatedDate", "Description", "IsActive", "Name", "Type" },
                values: new object[] { 3, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9872), null, true, "وزارة التعليم", 0 });

            migrationBuilder.InsertData(
                table: "Organizations",
                columns: new[] { "Id", "CreatedDate", "Description", "IsActive", "Name", "Type" },
                values: new object[] { 4, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9874), null, true, "جامعة الملك سعود", 2 });

            migrationBuilder.InsertData(
                table: "Organizations",
                columns: new[] { "Id", "CreatedDate", "Description", "IsActive", "Name", "Type" },
                values: new object[] { 5, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9876), null, true, "شركة أرامكو السعودية", 1 });

            migrationBuilder.InsertData(
                table: "Folders",
                columns: new[] { "Id", "CreatedDate", "DepartmentId", "IsActive", "Name" },
                values: new object[] { 1, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9580), 1, true, "المراسلات العامة" });

            migrationBuilder.InsertData(
                table: "Folders",
                columns: new[] { "Id", "CreatedDate", "DepartmentId", "IsActive", "Name" },
                values: new object[] { 2, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9584), 1, true, "القرارات الإدارية" });

            migrationBuilder.InsertData(
                table: "Folders",
                columns: new[] { "Id", "CreatedDate", "DepartmentId", "IsActive", "Name" },
                values: new object[] { 3, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9586), 2, true, "الميزانية" });

            migrationBuilder.InsertData(
                table: "Folders",
                columns: new[] { "Id", "CreatedDate", "DepartmentId", "IsActive", "Name" },
                values: new object[] { 4, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9588), 2, true, "المصروفات" });

            migrationBuilder.InsertData(
                table: "Folders",
                columns: new[] { "Id", "CreatedDate", "DepartmentId", "IsActive", "Name" },
                values: new object[] { 5, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9590), 3, true, "التوظيف" });

            migrationBuilder.InsertData(
                table: "Folders",
                columns: new[] { "Id", "CreatedDate", "DepartmentId", "IsActive", "Name" },
                values: new object[] { 6, new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9592), 4, true, "العقود" });

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_DocumentId",
                table: "Attachments",
                column: "DocumentId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentHyperlinks_DocumentId",
                table: "DocumentHyperlinks",
                column: "DocumentId");

            migrationBuilder.CreateIndex(
                name: "IX_Documents_DepartmentId",
                table: "Documents",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Documents_FolderId",
                table: "Documents",
                column: "FolderId");

            migrationBuilder.CreateIndex(
                name: "IX_Documents_OrganizationId",
                table: "Documents",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_Folders_DepartmentId",
                table: "Folders",
                column: "DepartmentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Attachments");

            migrationBuilder.DropTable(
                name: "DocumentHyperlinks");

            migrationBuilder.DropTable(
                name: "Documents");

            migrationBuilder.DropTable(
                name: "Folders");

            migrationBuilder.DropTable(
                name: "Organizations");

            migrationBuilder.DropTable(
                name: "Departments");
        }
    }
}
