using System.ComponentModel.DataAnnotations;

namespace Archif.Models
{
    public enum DocumentType
    {
        Incoming = 0,  // وارد
        Outgoing = 1   // صادر
    }

    public class Document
    {
        public int Id { get; set; }

        public int SequenceNumber { get; set; }

        [Required]
        [StringLength(100)]
        public string DocumentNumber { get; set; } = string.Empty;

        public DateTime DocumentDate { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(1000)]
        public string Subject { get; set; } = string.Empty;

        public DocumentType Type { get; set; }

        [StringLength(200)]
        public string FromTo { get; set; } = string.Empty;

        public int DepartmentId { get; set; }

        public int FolderId { get; set; }

        public int? OrganizationId { get; set; }

        [StringLength(50)]
        public string ArchiveSequence { get; set; } = string.Empty;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual Department Department { get; set; } = null!;
        public virtual Folder Folder { get; set; } = null!;
        public virtual Organization? Organization { get; set; }
        public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();
        public virtual ICollection<DocumentHyperlink> Hyperlinks { get; set; } = new List<DocumentHyperlink>();
    }

    /// <summary>
    /// نموذج الروابط المرتبطة بالوثيقة
    /// </summary>
    public class DocumentHyperlink
    {
        public int Id { get; set; }

        public int DocumentId { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Url { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Document Document { get; set; } = null!;
    }

    /// <summary>
    /// أنواع الجهات
    /// </summary>
    public enum OrganizationType
    {
        Government = 0,    // حكومية
        Private = 1,       // خاصة
        Academic = 2,      // أكاديمية
        NonProfit = 3,     // غير ربحية
        International = 4  // دولية
    }

    /// <summary>
    /// نموذج الجهات الحكومية والمؤسسات
    /// </summary>
    public class Organization
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public OrganizationType Type { get; set; } = OrganizationType.Government;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    }
}
