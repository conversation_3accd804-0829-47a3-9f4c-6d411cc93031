<Window x:Class="Archif.Views.HyperlinksManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الروابط" 
        Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Padding="20,15" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="24" Height="24" Fill="#4A90E2" VerticalAlignment="Center"/>
                <TextBlock Text="إدارة الروابط" 
                          FontSize="18" FontWeight="Bold" 
                          Margin="10,0,0,0" VerticalAlignment="Center"
                          Foreground="#333333"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة الروابط -->
            <Border Grid.Column="0" Margin="0,0,10,0" Padding="15" 
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <StackPanel>
                    <TextBlock Text="الروابط المضافة" 
                              FontSize="16" FontWeight="Bold" 
                              Margin="0,0,0,15" Foreground="#333333"/>
                    
                    <DataGrid x:Name="HyperlinksDataGrid" 
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             Background="White"
                             BorderThickness="0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="العنوان" 
                                              Binding="{Binding Title}" 
                                              Width="150"/>
                            <DataGridTextColumn Header="الرابط" 
                                              Binding="{Binding Url}" 
                                              Width="*"/>
                            <DataGridTextColumn Header="الوصف" 
                                              Binding="{Binding Description}" 
                                              Width="200"/>
                            <DataGridTemplateColumn Header="العمليات" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="فتح" 
                                                   Background="Transparent" 
                                                   Foreground="#4A90E2" 
                                                   BorderThickness="0" 
                                                   Padding="5" 
                                                   Margin="2"
                                                   Click="OpenHyperlink_Click"
                                                   Tag="{Binding}"/>
                                            <Button Content="حذف" 
                                                   Background="Transparent" 
                                                   Foreground="#DC3545" 
                                                   BorderThickness="0" 
                                                   Padding="5" 
                                                   Margin="2"
                                                   Click="DeleteHyperlink_Click"
                                                   Tag="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Border>

            <!-- نموذج إضافة رابط جديد -->
            <Border Grid.Column="1" Margin="10,0,0,0" Padding="20" 
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <StackPanel>
                    <TextBlock Text="إضافة رابط جديد" 
                              FontSize="16" FontWeight="Bold" 
                              Margin="0,0,0,20" Foreground="#333333"/>

                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="عنوان الرابط *" 
                                  Margin="0,0,0,5" Foreground="#666666"/>
                        <TextBox x:Name="TitleTextBox" 
                                Height="35" Padding="10,8"
                                BorderBrush="#E0E0E0" BorderThickness="1"/>
                    </StackPanel>

                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="الرابط (URL) *" 
                                  Margin="0,0,0,5" Foreground="#666666"/>
                        <TextBox x:Name="UrlTextBox" 
                                Height="35" Padding="10,8"
                                BorderBrush="#E0E0E0" BorderThickness="1"/>
                    </StackPanel>

                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="الوصف" 
                                  Margin="0,0,0,5" Foreground="#666666"/>
                        <TextBox x:Name="DescriptionTextBox" 
                                Height="80" Padding="10,8"
                                TextWrapping="Wrap" AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"
                                BorderBrush="#E0E0E0" BorderThickness="1"/>
                    </StackPanel>

                    <Button x:Name="AddHyperlinkButton"
                           Content="إضافة الرابط"
                           Background="#4A90E2"
                           Foreground="White"
                           BorderThickness="0"
                           Height="35"
                           Click="AddHyperlinkButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" 
               Background="White"
               BorderThickness="0,1,0,0"
               BorderBrush="#E0E0E0"
               Padding="20,15">
            
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center">
                <Button x:Name="CloseButton"
                       Content="إغلاق"
                       Background="White"
                       Foreground="#666666"
                       BorderBrush="#E0E0E0"
                       BorderThickness="1"
                       Width="100"
                       Height="35"
                       Margin="10,0"
                       Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
