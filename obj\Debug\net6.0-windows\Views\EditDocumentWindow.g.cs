﻿#pragma checksum "..\..\..\..\Views\EditDocumentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C5274D9AECEE75EDFA7895DABFE401D1DCFCABF8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Archif.Views {
    
    
    /// <summary>
    /// EditDocumentWindow
    /// </summary>
    public partial class EditDocumentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 23 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitleText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DocumentNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DocumentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton OutgoingRadio;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton IncomingRadio;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ArchiveSequenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DepartmentComboBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FolderComboBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OrganizationComboBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SubjectTextBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SequenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFilesButton;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanButton;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFromCameraButton;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AttachmentsListBox;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\Views\EditDocumentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Archif;V2.0.0.0;component/views/editdocumentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EditDocumentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DocumentNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.DocumentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.OutgoingRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 83 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.OutgoingRadio.Checked += new System.Windows.RoutedEventHandler(this.DocumentType_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.IncomingRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 85 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.IncomingRadio.Checked += new System.Windows.RoutedEventHandler(this.DocumentType_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ArchiveSequenceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 94 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.ArchiveSequenceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ArchiveSequenceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DepartmentComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 105 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.DepartmentComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DepartmentComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.FolderComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.OrganizationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.SubjectTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.SequenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.AddFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.AddFilesButton.Click += new System.Windows.RoutedEventHandler(this.AddFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ScanButton = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.ScanButton.Click += new System.Windows.RoutedEventHandler(this.ScanButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.AddFromCameraButton = ((System.Windows.Controls.Button)(target));
            
            #line 190 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.AddFromCameraButton.Click += new System.Windows.RoutedEventHandler(this.AddFromCameraButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.AttachmentsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 19:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 261 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 271 "..\..\..\..\Views\EditDocumentWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 233 "..\..\..\..\Views\EditDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 238 "..\..\..\..\Views\EditDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 243 "..\..\..\..\Views\EditDocumentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveAttachment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

