﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>

    <AssemblyTitle>نظام الأرشفة الإلكترونية</AssemblyTitle>
    <AssemblyDescription>نظام احترافي لإدارة الوثائق الصادرة والواردة</AssemblyDescription>
    <AssemblyCompany>شركة التطوير</AssemblyCompany>
    <AssemblyProduct>Archif</AssemblyProduct>
    <Version>*******</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.25" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="System.Text.Json" Version="6.0.0" />
    <PackageReference Include="ClosedXML" Version="0.102.1" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\Strings.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Strings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Strings.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Resources\" />
    <Folder Include="Helpers\" />
  </ItemGroup>

</Project>
