using System;
using System.Globalization;
using System.Resources;
using System.Threading;
using System.Windows;
using FlowDirection = System.Windows.FlowDirection;
using Application = System.Windows.Application;

namespace Archif.Helpers
{
    /// <summary>
    /// مدير التوطين والترجمة للتطبيق
    /// </summary>
    public static class LocalizationManager
    {
        private static System.Resources.ResourceManager _resourceManager;
        private static CultureInfo _currentCulture;

        static LocalizationManager()
        {
            // تهيئة مدير الموارد
            _resourceManager = new System.Resources.ResourceManager("Archif.Resources.Strings", typeof(LocalizationManager).Assembly);

            // تعيين الثقافة العربية كافتراضية
            SetCulture("ar-SA");
        }

        /// <summary>
        /// تعيين ثقافة التطبيق
        /// </summary>
        public static void SetCulture(string cultureName)
        {
            try
            {
                _currentCulture = new CultureInfo(cultureName);
                
                // تعيين الثقافة للخيط الحالي
                Thread.CurrentThread.CurrentCulture = _currentCulture;
                Thread.CurrentThread.CurrentUICulture = _currentCulture;
                
                // تعيين الثقافة للتطبيق
                CultureInfo.DefaultThreadCurrentCulture = _currentCulture;
                CultureInfo.DefaultThreadCurrentUICulture = _currentCulture;

                // تحديث اتجاه النص في WPF
                if (_currentCulture.TextInfo.IsRightToLeft)
                {
                    FrameworkElement.FlowDirectionProperty.OverrideMetadata(
                        typeof(FrameworkElement),
                        new FrameworkPropertyMetadata(FlowDirection.RightToLeft));
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تعيين ثقافة التطبيق");
                // في حالة الفشل، استخدم الثقافة الافتراضية
                _currentCulture = CultureInfo.CurrentCulture;
            }
        }

        /// <summary>
        /// الحصول على نص مترجم
        /// </summary>
        public static string GetString(string key)
        {
            try
            {
                var value = _resourceManager.GetString(key, _currentCulture);
                return value ?? $"[{key}]"; // إرجاع المفتاح إذا لم يتم العثور على الترجمة
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, $"الحصول على النص المترجم: {key}");
                return $"[{key}]";
            }
        }

        /// <summary>
        /// الحصول على نص مترجم مع معاملات
        /// </summary>
        public static string GetString(string key, params object[] args)
        {
            try
            {
                var format = GetString(key);
                return string.Format(format, args);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, $"تنسيق النص المترجم: {key}");
                return $"[{key}]";
            }
        }

        /// <summary>
        /// الحصول على الثقافة الحالية
        /// </summary>
        public static CultureInfo CurrentCulture => _currentCulture;

        /// <summary>
        /// التحقق من كون الثقافة الحالية عربية
        /// </summary>
        public static bool IsArabicCulture => _currentCulture.Name.StartsWith("ar");

        /// <summary>
        /// التحقق من كون الثقافة الحالية RTL
        /// </summary>
        public static bool IsRightToLeft => _currentCulture.TextInfo.IsRightToLeft;

        /// <summary>
        /// تنسيق التاريخ حسب الثقافة الحالية
        /// </summary>
        public static string FormatDate(DateTime date, string format = "d")
        {
            try
            {
                return date.ToString(format, _currentCulture);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنسيق التاريخ");
                return date.ToString();
            }
        }

        /// <summary>
        /// تنسيق التاريخ والوقت حسب الثقافة الحالية
        /// </summary>
        public static string FormatDateTime(DateTime dateTime, string format = "g")
        {
            try
            {
                return dateTime.ToString(format, _currentCulture);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنسيق التاريخ والوقت");
                return dateTime.ToString();
            }
        }

        /// <summary>
        /// تنسيق الرقم حسب الثقافة الحالية
        /// </summary>
        public static string FormatNumber(decimal number, string format = "N0")
        {
            try
            {
                return number.ToString(format, _currentCulture);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنسيق الرقم");
                return number.ToString();
            }
        }

        /// <summary>
        /// تنسيق العملة حسب الثقافة الحالية
        /// </summary>
        public static string FormatCurrency(decimal amount, string format = "C")
        {
            try
            {
                return amount.ToString(format, _currentCulture);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنسيق العملة");
                return amount.ToString();
            }
        }

        /// <summary>
        /// تحويل الأرقام إلى الأرقام العربية-الهندية
        /// </summary>
        public static string ConvertToArabicNumerals(string text)
        {
            if (string.IsNullOrEmpty(text) || !IsArabicCulture)
                return text;

            try
            {
                var arabicNumerals = new char[] { '٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩' };
                var result = text.ToCharArray();

                for (int i = 0; i < result.Length; i++)
                {
                    if (char.IsDigit(result[i]))
                    {
                        result[i] = arabicNumerals[result[i] - '0'];
                    }
                }

                return new string(result);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحويل الأرقام العربية");
                return text;
            }
        }

        /// <summary>
        /// تحويل الأرقام العربية-الهندية إلى الأرقام الإنجليزية
        /// </summary>
        public static string ConvertToEnglishNumerals(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            try
            {
                var arabicNumerals = new char[] { '٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩' };
                var result = text.ToCharArray();

                for (int i = 0; i < result.Length; i++)
                {
                    var index = Array.IndexOf(arabicNumerals, result[i]);
                    if (index >= 0)
                    {
                        result[i] = (char)('0' + index);
                    }
                }

                return new string(result);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحويل الأرقام الإنجليزية");
                return text;
            }
        }

        /// <summary>
        /// الحصول على اسم اليوم بالعربية
        /// </summary>
        public static string GetDayName(DayOfWeek dayOfWeek)
        {
            try
            {
                return _currentCulture.DateTimeFormat.GetDayName(dayOfWeek);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "الحصول على اسم اليوم");
                return dayOfWeek.ToString();
            }
        }

        /// <summary>
        /// الحصول على اسم الشهر بالعربية
        /// </summary>
        public static string GetMonthName(int month)
        {
            try
            {
                return _currentCulture.DateTimeFormat.GetMonthName(month);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "الحصول على اسم الشهر");
                return month.ToString();
            }
        }

        /// <summary>
        /// تحديث موارد التطبيق
        /// </summary>
        public static void RefreshResources()
        {
            try
            {
                // إعادة تحميل مدير الموارد
                _resourceManager = new System.Resources.ResourceManager("Archif.Resources.Strings", typeof(LocalizationManager).Assembly);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحديث موارد التطبيق");
            }
        }
    }

    /// <summary>
    /// امتداد لتسهيل الوصول للنصوص المترجمة
    /// </summary>
    public static class LocalizationExtensions
    {
        /// <summary>
        /// الحصول على نص مترجم
        /// </summary>
        public static string Localize(this string key)
        {
            return LocalizationManager.GetString(key);
        }

        /// <summary>
        /// الحصول على نص مترجم مع معاملات
        /// </summary>
        public static string Localize(this string key, params object[] args)
        {
            return LocalizationManager.GetString(key, args);
        }
    }
}
