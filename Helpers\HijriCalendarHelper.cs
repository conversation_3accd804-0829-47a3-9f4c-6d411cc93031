using System;
using System.Globalization;

namespace Archif.Helpers
{
    /// <summary>
    /// مساعد التقويم الهجري مع دعم التحويل والتنسيق
    /// </summary>
    public static class HijriCalendarHelper
    {
        private static readonly HijriCalendar _hijriCalendar = new HijriCalendar();
        private static readonly string[] _hijriMonthNames = {
            "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
            "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
        };

        private static readonly string[] _hijriDayNames = {
            "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"
        };

        /// <summary>
        /// تحويل التاريخ الميلادي إلى هجري
        /// </summary>
        public static HijriDate ConvertToHijri(DateTime gregorianDate)
        {
            try
            {
                var hijriYear = _hijriCalendar.GetYear(gregorianDate);
                var hijriMonth = _hijriCalendar.GetMonth(gregorianDate);
                var hijriDay = _hijriCalendar.GetDayOfMonth(gregorianDate);
                var hijriDayOfWeek = _hijriCalendar.GetDayOfWeek(gregorianDate);

                return new HijriDate
                {
                    Year = hijriYear,
                    Month = hijriMonth,
                    Day = hijriDay,
                    DayOfWeek = hijriDayOfWeek,
                    GregorianDate = gregorianDate
                };
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحويل التاريخ إلى هجري");
                return new HijriDate
                {
                    Year = 1445,
                    Month = 1,
                    Day = 1,
                    DayOfWeek = DayOfWeek.Sunday,
                    GregorianDate = gregorianDate
                };
            }
        }

        /// <summary>
        /// تحويل التاريخ الهجري إلى ميلادي
        /// </summary>
        public static DateTime ConvertToGregorian(int hijriYear, int hijriMonth, int hijriDay)
        {
            try
            {
                return _hijriCalendar.ToDateTime(hijriYear, hijriMonth, hijriDay, 0, 0, 0, 0);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحويل التاريخ إلى ميلادي");
                return DateTime.Now;
            }
        }

        /// <summary>
        /// تنسيق التاريخ الهجري
        /// </summary>
        public static string FormatHijriDate(DateTime gregorianDate, HijriDateFormat format = HijriDateFormat.Long)
        {
            try
            {
                var hijriDate = ConvertToHijri(gregorianDate);
                return FormatHijriDate(hijriDate, format);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنسيق التاريخ الهجري");
                return gregorianDate.ToString("yyyy/MM/dd");
            }
        }

        /// <summary>
        /// تنسيق التاريخ الهجري
        /// </summary>
        public static string FormatHijriDate(HijriDate hijriDate, HijriDateFormat format = HijriDateFormat.Long)
        {
            try
            {
                return format switch
                {
                    HijriDateFormat.Short => $"{hijriDate.Day:00}/{hijriDate.Month:00}/{hijriDate.Year}",
                    HijriDateFormat.Medium => $"{hijriDate.Day} {GetHijriMonthName(hijriDate.Month)} {hijriDate.Year}هـ",
                    HijriDateFormat.Long => $"{GetHijriDayName(hijriDate.DayOfWeek)} {hijriDate.Day} {GetHijriMonthName(hijriDate.Month)} {hijriDate.Year}هـ",
                    HijriDateFormat.Full => $"يوم {GetHijriDayName(hijriDate.DayOfWeek)} الموافق {hijriDate.Day} من شهر {GetHijriMonthName(hijriDate.Month)} لعام {hijriDate.Year} هجرية",
                    _ => $"{hijriDate.Day} {GetHijriMonthName(hijriDate.Month)} {hijriDate.Year}هـ"
                };
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنسيق التاريخ الهجري");
                return $"{hijriDate.Day}/{hijriDate.Month}/{hijriDate.Year}هـ";
            }
        }

        /// <summary>
        /// الحصول على اسم الشهر الهجري
        /// </summary>
        public static string GetHijriMonthName(int month)
        {
            try
            {
                if (month >= 1 && month <= 12)
                {
                    return _hijriMonthNames[month - 1];
                }
                return "شهر غير صحيح";
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "الحصول على اسم الشهر الهجري");
                return $"الشهر {month}";
            }
        }

        /// <summary>
        /// الحصول على اسم اليوم بالعربية
        /// </summary>
        public static string GetHijriDayName(DayOfWeek dayOfWeek)
        {
            try
            {
                return _hijriDayNames[(int)dayOfWeek];
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "الحصول على اسم اليوم");
                return dayOfWeek.ToString();
            }
        }

        /// <summary>
        /// الحصول على التاريخ الهجري الحالي
        /// </summary>
        public static HijriDate GetCurrentHijriDate()
        {
            return ConvertToHijri(DateTime.Now);
        }

        /// <summary>
        /// التحقق من صحة التاريخ الهجري
        /// </summary>
        public static bool IsValidHijriDate(int year, int month, int day)
        {
            try
            {
                if (year < 1 || year > 1500) return false;
                if (month < 1 || month > 12) return false;
                if (day < 1 || day > 30) return false;

                // التحقق من صحة التاريخ بمحاولة تحويله
                var gregorianDate = _hijriCalendar.ToDateTime(year, month, day, 0, 0, 0, 0);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على عدد أيام الشهر الهجري
        /// </summary>
        public static int GetDaysInHijriMonth(int year, int month)
        {
            try
            {
                return _hijriCalendar.GetDaysInMonth(year, month);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "الحصول على عدد أيام الشهر الهجري");
                return 30; // القيمة الافتراضية
            }
        }

        /// <summary>
        /// الحصول على عدد أيام السنة الهجرية
        /// </summary>
        public static int GetDaysInHijriYear(int year)
        {
            try
            {
                return _hijriCalendar.GetDaysInYear(year);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "الحصول على عدد أيام السنة الهجرية");
                return 354; // القيمة الافتراضية
            }
        }

        /// <summary>
        /// إضافة أيام للتاريخ الهجري
        /// </summary>
        public static HijriDate AddDaysToHijriDate(HijriDate hijriDate, int days)
        {
            try
            {
                var gregorianDate = hijriDate.GregorianDate.AddDays(days);
                return ConvertToHijri(gregorianDate);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "إضافة أيام للتاريخ الهجري");
                return hijriDate;
            }
        }

        /// <summary>
        /// إضافة أشهر للتاريخ الهجري
        /// </summary>
        public static HijriDate AddMonthsToHijriDate(HijriDate hijriDate, int months)
        {
            try
            {
                var newMonth = hijriDate.Month + months;
                var newYear = hijriDate.Year;

                while (newMonth > 12)
                {
                    newMonth -= 12;
                    newYear++;
                }

                while (newMonth < 1)
                {
                    newMonth += 12;
                    newYear--;
                }

                // التأكد من صحة اليوم في الشهر الجديد
                var daysInNewMonth = GetDaysInHijriMonth(newYear, newMonth);
                var newDay = Math.Min(hijriDate.Day, daysInNewMonth);

                var gregorianDate = ConvertToGregorian(newYear, newMonth, newDay);
                return ConvertToHijri(gregorianDate);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "إضافة أشهر للتاريخ الهجري");
                return hijriDate;
            }
        }

        /// <summary>
        /// حساب الفرق بين تاريخين هجريين بالأيام
        /// </summary>
        public static int GetDaysDifference(HijriDate date1, HijriDate date2)
        {
            try
            {
                var timeSpan = date2.GregorianDate - date1.GregorianDate;
                return (int)timeSpan.TotalDays;
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "حساب الفرق بين التواريخ");
                return 0;
            }
        }

        /// <summary>
        /// تحويل النص إلى تاريخ هجري
        /// </summary>
        public static bool TryParseHijriDate(string dateText, out HijriDate hijriDate)
        {
            hijriDate = new HijriDate();

            try
            {
                // تنظيف النص
                dateText = dateText?.Trim().Replace("هـ", "").Replace("ه", "");

                if (string.IsNullOrEmpty(dateText))
                    return false;

                // محاولة تحليل التنسيقات المختلفة
                var parts = dateText.Split(new char[] { '/', '-', ' ' }, StringSplitOptions.RemoveEmptyEntries);

                if (parts.Length >= 3)
                {
                    if (int.TryParse(parts[0], out int day) &&
                        int.TryParse(parts[1], out int month) &&
                        int.TryParse(parts[2], out int year))
                    {
                        if (IsValidHijriDate(year, month, day))
                        {
                            var gregorianDate = ConvertToGregorian(year, month, day);
                            hijriDate = ConvertToHijri(gregorianDate);
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحليل التاريخ الهجري");
                return false;
            }
        }
    }

    /// <summary>
    /// بنية التاريخ الهجري
    /// </summary>
    public struct HijriDate
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public int Day { get; set; }
        public DayOfWeek DayOfWeek { get; set; }
        public DateTime GregorianDate { get; set; }

        public override string ToString()
        {
            return HijriCalendarHelper.FormatHijriDate(this, HijriDateFormat.Medium);
        }
    }

    /// <summary>
    /// تنسيقات التاريخ الهجري
    /// </summary>
    public enum HijriDateFormat
    {
        Short,      // 01/01/1445
        Medium,     // 1 محرم 1445هـ
        Long,       // الأحد 1 محرم 1445هـ
        Full        // يوم الأحد الموافق 1 من شهر محرم لعام 1445 هجرية
    }
}
