using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Diagnostics;
using Microsoft.Win32;
using Archif.Services;
using Archif.Models;
using Archif.Helpers;
using WpfMessageBox = System.Windows.MessageBox;
using WpfOpenFileDialog = Microsoft.Win32.OpenFileDialog;
using WpfButton = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// نافذة إضافة وثيقة جديدة
    /// </summary>
    public partial class AddDocumentWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly FileService _fileService;
        private readonly ObservableCollection<AttachmentInfo> _attachments;
        private static bool _isInstanceOpen = false;

        public event EventHandler? DocumentAdded;

        public AddDocumentWindow(DatabaseService databaseService)
        {
            // التحقق من عدم وجود نافذة مفتوحة
            if (_isInstanceOpen)
            {
                throw new InvalidOperationException("نافذة إضافة الوثيقة مفتوحة بالفعل");
            }

            InitializeComponent();
            _databaseService = databaseService;
            _fileService = new FileService();
            _attachments = new ObservableCollection<AttachmentInfo>();

            AttachmentsListBox.ItemsSource = _attachments;

            // تعيين حالة النافذة
            _isInstanceOpen = true;

            // إعداد أحداث النافذة
            Loaded += AddDocumentWindow_Loaded;
            Closed += AddDocumentWindow_Closed;

            InitializeForm();
        }

        private void AddDocumentWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // التأكد من أن النافذة في المقدمة
                this.Activate();
                this.Focus();

                ErrorHandler.LogInfo("تم فتح نافذة إضافة الوثيقة", "AddDocumentWindow.Loaded");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تحميل نافذة إضافة الوثيقة");
            }
        }

        private void AddDocumentWindow_Closed(object sender, EventArgs e)
        {
            try
            {
                // إعادة تعيين حالة النافذة
                _isInstanceOpen = false;

                ErrorHandler.LogInfo("تم إغلاق نافذة إضافة الوثيقة", "AddDocumentWindow.Closed");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "إغلاق نافذة إضافة الوثيقة");
            }
        }

        private async void InitializeForm()
        {
            try
            {
                // تعيين التاريخ الحالي
                CreatedDatePicker.SelectedDate = DateTime.Now;
                DocumentDatePicker.SelectedDate = DateTime.Now;

                // تحميل التسلسل التلقائي
                var nextSequence = await _databaseService.GetNextSequenceNumberAsync();
                SequenceTextBox.Text = nextSequence.ToString();

                // تعيين تسلسل حفظ افتراضي (الرقم فقط)
                ArchiveSequenceTextBox.Text = nextSequence.ToString();

                // تحميل الأقسام
                await LoadDepartments();

                // تحميل الجهات
                await LoadOrganizations();

                // تحديث بادئة تسلسل الحفظ
                UpdateArchiveSequencePrefix();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تهيئة النموذج");
            }
        }

        private async Task LoadDepartments()
        {
            var departments = await _databaseService.GetDepartmentsAsync();

            DepartmentComboBox.Items.Clear();
            foreach (var department in departments)
            {
                DepartmentComboBox.Items.Add(new ComboBoxItem
                {
                    Content = department.Name,
                    Tag = department
                });
            }
        }

        private async void DepartmentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DepartmentComboBox.SelectedItem is ComboBoxItem selectedItem &&
                selectedItem.Tag is Department department)
            {
                await LoadFolders(department.Id);
            }
        }

        private async Task LoadFolders(int departmentId)
        {
            var folders = await _databaseService.GetFoldersByDepartmentAsync(departmentId);

            FolderComboBox.Items.Clear();
            foreach (var folder in folders)
            {
                FolderComboBox.Items.Add(new ComboBoxItem
                {
                    Content = folder.Name,
                    Tag = folder
                });
            }
        }

        private async Task LoadOrganizations()
        {
            try
            {
                var organizations = await _databaseService.GetOrganizationsAsync();

                OrganizationComboBox.Items.Clear();

                // إضافة خيار فارغ
                OrganizationComboBox.Items.Add(new ComboBoxItem
                {
                    Content = "-- اختر الجهة --",
                    Tag = null
                });

                foreach (var organization in organizations)
                {
                    OrganizationComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = organization.Name,
                        Tag = organization
                    });
                }

                // تحديد الخيار الفارغ كافتراضي
                OrganizationComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحميل الجهات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void DocumentType_Changed(object sender, RoutedEventArgs e)
        {
            UpdateArchiveSequencePrefix();
            ValidateArchiveSequenceFormat(); // التحقق من التنسيق بعد تغيير النوع
        }

        private void UpdateArchiveSequencePrefix()
        {
            try
            {
                var currentText = ArchiveSequenceTextBox.Text;

                // إذا كان النص فارغاً أو يحتوي على البادئة القديمة، قم بتحديثه
                if (string.IsNullOrWhiteSpace(currentText) ||
                    currentText.StartsWith("S-") || currentText.StartsWith("W-"))
                {
                    // استخراج الرقم إذا كان موجوداً، أو استخدام التسلسل التلقائي
                    var sequenceNumber = 1;
                    if (!string.IsNullOrWhiteSpace(currentText))
                    {
                        var parts = currentText.Split('-');
                        if (parts.Length == 3 && int.TryParse(parts[2], out int extractedNumber))
                        {
                            sequenceNumber = extractedNumber;
                        }
                    }
                    else if (!string.IsNullOrWhiteSpace(SequenceTextBox.Text))
                    {
                        // استخدام التسلسل التلقائي إذا كان متاحاً
                        if (int.TryParse(SequenceTextBox.Text, out int autoSequence))
                        {
                            sequenceNumber = autoSequence;
                        }
                    }

                    // عرض الرقم فقط بدون أصفار زائدة
                    ArchiveSequenceTextBox.Text = sequenceNumber.ToString();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث بادئة التسلسل: {ex.Message}");
            }
        }

        private void ArchiveSequenceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // يمكن إضافة التحقق من صحة التنسيق هنا إذا لزم الأمر
            ValidateArchiveSequenceFormat();
        }

        private void ValidateArchiveSequenceFormat()
        {
            try
            {
                var text = ArchiveSequenceTextBox.Text;
                if (!string.IsNullOrWhiteSpace(text))
                {
                    // التحقق من أن النص يحتوي على أرقام فقط
                    if (int.TryParse(text, out int number) && number > 0)
                    {
                        ArchiveSequenceTextBox.BorderBrush = new SolidColorBrush(Colors.Green);
                        ArchiveSequenceTextBox.ToolTip = "تنسيق صحيح";
                    }
                    else
                    {
                        ArchiveSequenceTextBox.BorderBrush = new SolidColorBrush(Colors.Red);
                        ArchiveSequenceTextBox.ToolTip = "يجب إدخال رقم صحيح أكبر من صفر";
                    }
                }
                else
                {
                    ArchiveSequenceTextBox.BorderBrush = new SolidColorBrush(Colors.LightGray);
                    ArchiveSequenceTextBox.ToolTip = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من تنسيق التسلسل: {ex.Message}");
            }
        }

        private async void AddFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new WpfOpenFileDialog
                {
                    Title = "اختر الملفات المراد إرفاقها",
                    Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pptx;*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.txt;*.zip;*.rar|" +
                            "ملفات PDF|*.pdf|" +
                            "مستندات Word|*.doc;*.docx|" +
                            "جداول Excel|*.xls;*.xlsx|" +
                            "عروض PowerPoint|*.ppt;*.pptx|" +
                            "الصور|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff|" +
                            "ملفات نصية|*.txt|" +
                            "ملفات مضغوطة|*.zip;*.rar|" +
                            "جميع الملفات|*.*",
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    AddFilesButton.IsEnabled = false;
                    AddFilesButton.Content = "جاري الإضافة...";

                    var addedCount = 0;
                    foreach (var fileName in openFileDialog.FileNames)
                    {
                        try
                        {
                            if (!_fileService.IsValidFileType(fileName))
                            {
                                WpfMessageBox.Show($"نوع الملف غير مدعوم: {Path.GetFileName(fileName)}", "تحذير",
                                              MessageBoxButton.OK, MessageBoxImage.Warning);
                                continue;
                            }

                            await AddAttachmentAsync(fileName);
                            addedCount++;
                        }
                        catch (Exception ex)
                        {
                            WpfMessageBox.Show($"خطأ في إضافة الملف {Path.GetFileName(fileName)}: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }

                    if (addedCount > 0)
                    {
                        WpfMessageBox.Show($"تم إضافة {addedCount} ملف بنجاح", "نجح",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في إضافة الملفات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                AddFilesButton.IsEnabled = true;
                var originalContent = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                originalContent.Children.Add(new TextBlock { Text = "📁", FontSize = 16, VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(0, 0, 5, 0) });
                originalContent.Children.Add(new TextBlock { Text = "إضافة ملفات", Margin = new Thickness(5, 0, 0, 0) });
                AddFilesButton.Content = originalContent;
            }
        }

        private Task AddAttachmentAsync(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var attachment = new AttachmentInfo
            {
                FileName = fileInfo.Name,
                FilePath = filePath,
                FileType = fileInfo.Extension.ToLower(),
                MimeType = GetMimeType(fileInfo.Extension),
                FileSize = fileInfo.Length,
                Type = DetermineAttachmentType(fileInfo.Extension),
                CreatedDate = DateTime.Now
            };

            _attachments.Add(attachment);
            return Task.CompletedTask;
        }

        private string GetMimeType(string extension)
        {
            return extension.ToLower() switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                ".txt" => "text/plain",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                _ => "application/octet-stream"
            };
        }

        private AttachmentType DetermineAttachmentType(string extension)
        {
            return extension.ToLower() switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" or ".tif" => AttachmentType.ScannedImage,
                ".pdf" => AttachmentType.ScannedPdf,
                _ => AttachmentType.Document
            };
        }

        private void RemoveAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is WpfButton button && button.Tag is AttachmentInfo attachment)
            {
                _attachments.Remove(attachment);
            }
        }

        private async void PreviewAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is WpfButton button && button.Tag is AttachmentInfo attachment)
            {
                try
                {
                    await _fileService.OpenAttachmentAsync(attachment.FilePath);
                }
                catch (Exception ex)
                {
                    WpfMessageBox.Show($"خطأ في معاينة الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void OpenAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is WpfButton button && button.Tag is AttachmentInfo attachment)
            {
                try
                {
                    await _fileService.OpenAttachmentAsync(attachment.FilePath);
                }
                catch (Exception ex)
                {
                    WpfMessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void AddFromCameraButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاولة فتح تطبيق الكاميرا في Windows
                var cameraProcess = new ProcessStartInfo
                {
                    FileName = "microsoft.windows.camera:",
                    UseShellExecute = true
                };

                Process.Start(cameraProcess);

                WpfMessageBox.Show("تم فتح تطبيق الكاميرا. بعد التقاط الصور، يمكنك إضافتها باستخدام زر 'إضافة ملفات'.",
                              "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"لم يتم العثور على تطبيق الكاميرا أو فشل في تشغيله.\n\nتفاصيل الخطأ: {ex.Message}",
                              "خطأ في الكاميرا", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ScanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاولة تشغيل تطبيق المسح الضوئي الافتراضي في Windows
                var scannerProcess = new ProcessStartInfo
                {
                    FileName = "WiaAcmgr.exe",
                    Arguments = "/ScannerDeviceDialog",
                    UseShellExecute = true
                };

                Process.Start(scannerProcess);
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"لم يتم العثور على ماسح ضوئي أو فشل في تشغيل تطبيق المسح.\n\nتفاصيل الخطأ: {ex.Message}",
                              "خطأ في المسح الضوئي", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshScannersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ScannerComboBox.Items.Clear();
                ScannerComboBox.Items.Add("الماسح الضوئي الافتراضي");
                ScannerComboBox.Items.Add("HP Scanner");
                ScannerComboBox.Items.Add("Canon Scanner");
                ScannerComboBox.Items.Add("Epson Scanner");

                if (ScannerComboBox.Items.Count > 0)
                {
                    ScannerComboBox.SelectedIndex = 0;
                }

                WpfMessageBox.Show("تم تحديث قائمة أجهزة المسح", "تم التحديث",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحديث أجهزة المسح: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                await SaveDocument();

                WpfMessageBox.Show("تم حفظ الوثيقة بنجاح", "نجح الحفظ",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DocumentAdded?.Invoke(this, EventArgs.Empty);
                ClearForm();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في حفظ الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateForm()
        {
            try
            {
                var validations = new List<ValidationHelper.ValidationResult>();

                // التحقق من رقم الكتاب
                var documentNumberValidation = ValidationHelper.ValidateDocumentNumber(DocumentNumberTextBox.Text);
                validations.Add(documentNumberValidation);
                ValidationHelper.ApplyValidationToControl(DocumentNumberTextBox, documentNumberValidation);

                // التحقق من موضوع الكتاب
                var subjectValidation = ValidationHelper.ValidateArabicText(
                    SubjectTextBox.Text, "موضوع الكتاب", true, 5, 500);
                validations.Add(subjectValidation);
                ValidationHelper.ApplyValidationToControl(SubjectTextBox, subjectValidation);

                // التحقق من التاريخ
                var dateValidation = ValidationHelper.ValidateDate(
                    DocumentDatePicker.SelectedDate, "تاريخ الكتاب", true,
                    DateTime.Now.AddYears(-10), DateTime.Now.AddDays(1));
                validations.Add(dateValidation);

                // التحقق من القسم
                if (DepartmentComboBox.SelectedItem == null)
                {
                    var deptValidation = new ValidationHelper.ValidationResult();
                    deptValidation.AddError("يجب اختيار القسم");
                    validations.Add(deptValidation);
                    ValidationHelper.ApplyValidationToControl(DepartmentComboBox, deptValidation);
                }

                // التحقق من الضبارة
                if (FolderComboBox.SelectedItem == null)
                {
                    var folderValidation = new ValidationHelper.ValidationResult();
                    folderValidation.AddError("يجب اختيار الضبارة");
                    validations.Add(folderValidation);
                    ValidationHelper.ApplyValidationToControl(FolderComboBox, folderValidation);
                }

                // التحقق من تسلسل الحفظ
                var archiveSequenceValidation = new ValidationHelper.ValidationResult { IsValid = true };
                if (string.IsNullOrWhiteSpace(ArchiveSequenceTextBox.Text))
                {
                    archiveSequenceValidation.AddError("تسلسل الحفظ مطلوب");
                }
                else if (!int.TryParse(ArchiveSequenceTextBox.Text, out int archiveSequence) || archiveSequence <= 0)
                {
                    archiveSequenceValidation.AddError("تسلسل الحفظ يجب أن يكون رقم صحيح أكبر من صفر");
                }
                validations.Add(archiveSequenceValidation);
                ValidationHelper.ApplyValidationToControl(ArchiveSequenceTextBox, archiveSequenceValidation);

                // دمج جميع نتائج التحقق
                var overallValidation = ValidationHelper.ValidateForm(validations.ToArray());

                if (!overallValidation.IsValid)
                {
                    ErrorHandler.ShowError(overallValidation.GetErrorsText(), "خطأ في التحقق من البيانات");

                    // التركيز على أول حقل يحتوي على خطأ
                    if (documentNumberValidation != null && !documentNumberValidation.IsValid)
                        DocumentNumberTextBox.Focus();
                    else if (subjectValidation != null && !subjectValidation.IsValid)
                        SubjectTextBox.Focus();
                    else if (DepartmentComboBox.SelectedItem == null)
                        DepartmentComboBox.Focus();
                    else if (FolderComboBox.SelectedItem == null)
                        FolderComboBox.Focus();
                    else if (!archiveSequenceValidation.IsValid)
                        ArchiveSequenceTextBox.Focus();

                    return false;
                }

                if (overallValidation.Warnings.Any())
                {
                    var warningMessage = overallValidation.GetWarningsText() + "\n\nهل تريد المتابعة؟";
                    if (!ErrorHandler.ShowConfirmation(warningMessage, "تحذيرات"))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "التحقق من صحة البيانات");
                return false;
            }
        }

        private async Task SaveDocument()
        {
            var departmentItem = DepartmentComboBox.SelectedItem as ComboBoxItem;
            var folderItem = FolderComboBox.SelectedItem as ComboBoxItem;
            var organizationItem = OrganizationComboBox.SelectedItem as ComboBoxItem;

            var department = departmentItem?.Tag as Department;
            var folder = folderItem?.Tag as Folder;
            var organization = organizationItem?.Tag as Organization;

            var document = new Document
            {
                DocumentNumber = DocumentNumberTextBox.Text.Trim(),
                DocumentDate = DocumentDatePicker.SelectedDate!.Value,
                Subject = SubjectTextBox.Text.Trim(),
                Type = OutgoingRadio.IsChecked == true ? DocumentType.Outgoing : DocumentType.Incoming,
                FromTo = string.Empty, // حذف حقول "من" و "إلى"
                DepartmentId = department!.Id,
                FolderId = folder!.Id,
                OrganizationId = organization?.Id,
                ArchiveSequence = ArchiveSequenceTextBox.Text.Trim(),
                CreatedDate = DateTime.Now
            };

            var savedDocument = await _databaseService.AddDocumentAsync(document);

            // حفظ المرفقات
            await SaveAttachments(savedDocument.Id);
        }

        private async Task SaveAttachments(int documentId)
        {
            try
            {
                foreach (var attachmentInfo in _attachments)
                {
                    // نسخ الملف إلى مجلد المرفقات
                    var savedAttachment = await _fileService.SaveAttachmentAsync(
                        attachmentInfo.FilePath, documentId, attachmentInfo.Type, attachmentInfo.Description);

                    // حفظ معلومات المرفق في قاعدة البيانات
                    var attachment = new Attachment
                    {
                        DocumentId = documentId,
                        FileName = savedAttachment.FileName,
                        FilePath = savedAttachment.FilePath,
                        FileType = savedAttachment.FileType,
                        MimeType = savedAttachment.MimeType,
                        FileSize = savedAttachment.FileSize,
                        Type = savedAttachment.Type,
                        Description = savedAttachment.Description,
                        CreatedDate = savedAttachment.CreatedDate
                    };

                    await _databaseService.AddAttachmentAsync(attachment);
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في حفظ المرفقات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }



        private async void ClearForm()
        {
            DocumentNumberTextBox.Clear();
            SubjectTextBox.Clear();
            DepartmentComboBox.SelectedItem = null;
            FolderComboBox.SelectedItem = null;
            OrganizationComboBox.SelectedIndex = 0; // العودة للخيار الافتراضي
            _attachments.Clear();

            DocumentDatePicker.SelectedDate = DateTime.Now;
            OutgoingRadio.IsChecked = true;

            // إعادة تحميل التسلسل التلقائي الجديد
            try
            {
                var nextSequence = await _databaseService.GetNextSequenceNumberAsync();
                SequenceTextBox.Text = nextSequence.ToString();

                // تعيين تسلسل حفظ جديد (الرقم فقط)
                ArchiveSequenceTextBox.Text = nextSequence.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التسلسل: {ex.Message}");
                ArchiveSequenceTextBox.Text = "1";
            }

            // لا نحتاج لاستدعاء InitializeForm مرة أخرى هنا
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
