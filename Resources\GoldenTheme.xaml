<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ألوان النظام الذهبي -->
    
    <!-- الألوان الأساسية الذهبية -->
    <Color x:Key="PrimaryGoldColor">#FFD700</Color>
    <Color x:Key="DarkGoldColor">#B8860B</Color>
    <Color x:Key="LightGoldColor">#FFF8DC</Color>
    <Color x:Key="DeepGoldColor">#DAA520</Color>
    <Color x:Key="PaleGoldColor">#FFFACD</Color>
    
    <!-- ألوان الخلفية -->
    <Color x:Key="BackgroundGoldColor">#FFFEF7</Color>
    <Color x:Key="SecondaryBackgroundColor">#FFF9E6</Color>
    <Color x:Key="CardBackgroundColor">#FFFFFF</Color>
    
    <!-- ألوان النص -->
    <Color x:Key="PrimaryTextColor">#2C1810</Color>
    <Color x:Key="SecondaryTextColor">#5D4E37</Color>
    <Color x:Key="AccentTextColor">#8B4513</Color>
    
    <!-- ألوان الحدود -->
    <Color x:Key="BorderGoldColor">#CD853F</Color>
    <Color x:Key="FocusBorderColor">#FF8C00</Color>
    <Color x:Key="HoverBorderColor">#DEB887</Color>
    
    <!-- ألوان الأزرار -->
    <Color x:Key="ButtonGoldColor">#DAA520</Color>
    <Color x:Key="ButtonHoverColor">#FFD700</Color>
    <Color x:Key="ButtonPressedColor">#B8860B</Color>
    
    <!-- تحويل الألوان إلى Brushes -->
    <SolidColorBrush x:Key="PrimaryGoldBrush" Color="{StaticResource PrimaryGoldColor}"/>
    <SolidColorBrush x:Key="DarkGoldBrush" Color="{StaticResource DarkGoldColor}"/>
    <SolidColorBrush x:Key="LightGoldBrush" Color="{StaticResource LightGoldColor}"/>
    <SolidColorBrush x:Key="DeepGoldBrush" Color="{StaticResource DeepGoldColor}"/>
    <SolidColorBrush x:Key="PaleGoldBrush" Color="{StaticResource PaleGoldColor}"/>
    
    <SolidColorBrush x:Key="BackgroundGoldBrush" Color="{StaticResource BackgroundGoldColor}"/>
    <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="{StaticResource SecondaryBackgroundColor}"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="{StaticResource CardBackgroundColor}"/>
    
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="AccentTextBrush" Color="{StaticResource AccentTextColor}"/>
    
    <SolidColorBrush x:Key="BorderGoldBrush" Color="{StaticResource BorderGoldColor}"/>
    <SolidColorBrush x:Key="FocusBorderBrush" Color="{StaticResource FocusBorderColor}"/>
    <SolidColorBrush x:Key="HoverBorderBrush" Color="{StaticResource HoverBorderColor}"/>
    
    <SolidColorBrush x:Key="ButtonGoldBrush" Color="{StaticResource ButtonGoldColor}"/>
    <SolidColorBrush x:Key="ButtonHoverBrush" Color="{StaticResource ButtonHoverColor}"/>
    <SolidColorBrush x:Key="ButtonPressedBrush" Color="{StaticResource ButtonPressedColor}"/>

    <!-- تدرجات ذهبية -->
    <LinearGradientBrush x:Key="GoldenGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource LightGoldColor}" Offset="0"/>
        <GradientStop Color="{StaticResource PaleGoldColor}" Offset="0.5"/>
        <GradientStop Color="{StaticResource BackgroundGoldColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="ButtonGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource PrimaryGoldColor}" Offset="0"/>
        <GradientStop Color="{StaticResource DeepGoldColor}" Offset="0.5"/>
        <GradientStop Color="{StaticResource DarkGoldColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="CardGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource CardBackgroundColor}" Offset="0"/>
        <GradientStop Color="{StaticResource SecondaryBackgroundColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- تأثيرات الظل -->
    <DropShadowEffect x:Key="GoldenShadowEffect" 
                      Color="{StaticResource DarkGoldColor}" 
                      BlurRadius="8" 
                      ShadowDepth="2" 
                      Opacity="0.3"/>
    
    <DropShadowEffect x:Key="CardShadowEffect" 
                      Color="{StaticResource BorderGoldColor}" 
                      BlurRadius="12" 
                      ShadowDepth="4" 
                      Opacity="0.2"/>
    
    <DropShadowEffect x:Key="ButtonShadowEffect" 
                      Color="{StaticResource DarkGoldColor}" 
                      BlurRadius="6" 
                      ShadowDepth="3" 
                      Opacity="0.4"/>

    <!-- أنماط النصوص -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Effect" Value="{StaticResource GoldenShadowEffect}"/>
    </Style>
    
    <Style x:Key="LabelTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,0,5"/>
    </Style>
    
    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource AccentTextBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,10,0,15"/>
    </Style>

    <!-- أنماط حقول الإدخال -->
    <Style x:Key="GoldenTextBoxStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="10,5"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Width" Value="300"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderGoldBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Effect="{StaticResource GoldenShadowEffect}">
                        <ScrollViewer x:Name="PART_ContentHost"
                                      Margin="{TemplateBinding Padding}"
                                      VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource FocusBorderBrush}"/>
                            <Setter Property="BorderThickness" Value="3"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource HoverBorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط ComboBox -->
    <Style x:Key="GoldenComboBoxStyle" TargetType="ComboBox">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="10,5"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Width" Value="300"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderGoldBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                Effect="{StaticResource GoldenShadowEffect}">
                            <Grid>
                                <ContentPresenter x:Name="ContentSite"
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"
                                                  HorizontalAlignment="Center"
                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"/>
                                <ToggleButton x:Name="ToggleButton"
                                              Grid.Column="2"
                                              Focusable="false"
                                              IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                              ClickMode="Press"
                                              Background="Transparent"
                                              BorderThickness="0"
                                              HorizontalAlignment="Right"
                                              Width="30">
                                    <Path x:Name="Arrow"
                                          Fill="{StaticResource DeepGoldBrush}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Data="M 0 0 L 4 4 L 8 0 Z"/>
                                </ToggleButton>
                            </Grid>
                        </Border>
                        <Popup x:Name="Popup"
                               Placement="Bottom"
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               AllowsTransparency="True"
                               Focusable="False"
                               PopupAnimation="Slide">
                            <Grid MaxHeight="200" MinWidth="{TemplateBinding ActualWidth}">
                                <Border Background="{StaticResource CardBackgroundBrush}"
                                        BorderBrush="{StaticResource BorderGoldBrush}"
                                        BorderThickness="2"
                                        CornerRadius="8"
                                        Effect="{StaticResource CardShadowEffect}">
                                    <ScrollViewer>
                                        <StackPanel IsItemsHost="True"/>
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource FocusBorderBrush}"/>
                            <Setter Property="BorderThickness" Value="3"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource HoverBorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط DatePicker -->
    <Style x:Key="GoldenDatePickerStyle" TargetType="DatePicker">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="10,5"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Width" Value="300"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderGoldBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
    </Style>

    <!-- أنماط الأزرار -->
    <Style x:Key="GoldenButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="Margin" Value="10,15"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Background" Value="{StaticResource ButtonGradientBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource DarkGoldBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Effect" Value="{StaticResource ButtonShadowEffect}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource ButtonHoverBrush}"/>
                            <Setter Property="BorderBrush" Value="{StaticResource FocusBorderBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource ButtonPressedBrush}"/>
                            <Setter Property="BorderBrush" Value="{StaticResource DarkGoldBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- نمط البطاقة الذهبية -->
    <Style x:Key="GoldenCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardGradientBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderGoldBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="CornerRadius" Value="15"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Effect" Value="{StaticResource CardShadowEffect}"/>
    </Style>

</ResourceDictionary>
