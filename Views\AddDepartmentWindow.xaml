<Window x:Class="Archif.Views.AddDepartmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة قسم جديد"
        Height="400" Width="500"
        MinHeight="350" MinWidth="450"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="#F5F5F5"
        ResizeMode="NoResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Padding="20,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#4A90E2" Offset="0"/>
                    <GradientStop Color="#357ABD" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📁" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0" Foreground="White"/>
                <TextBlock Text="إضافة قسم جديد"
                          FontSize="18" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Border Grid.Row="1" Margin="20" Padding="25"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel>
                <!-- اسم القسم -->
                <StackPanel Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="📝" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="اسم القسم" FontSize="14" FontWeight="SemiBold"
                                  Foreground="#333333" VerticalAlignment="Center"/>
                        <TextBlock Text="*" Foreground="Red" FontSize="16" Margin="5,0,0,0"/>
                    </StackPanel>
                    <TextBox x:Name="DepartmentNameTextBox"
                            Height="40" Padding="12,10"
                            FontSize="14"
                            BorderBrush="#E0E0E0"
                            BorderThickness="2"
                            Background="White"
                            Foreground="#333333"/>
                    <TextBlock x:Name="NameValidationText"
                              Text="يرجى إدخال اسم القسم"
                              Foreground="Red" FontSize="12"
                              Margin="5,5,0,0" Visibility="Collapsed"/>
                </StackPanel>

                <!-- تاريخ الإنشاء -->
                <StackPanel Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="📅" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="تاريخ إنشاء القسم" FontSize="14" FontWeight="SemiBold"
                                  Foreground="#333333" VerticalAlignment="Center"/>
                    </StackPanel>
                    <DatePicker x:Name="CreatedDatePicker"
                               Height="40" Padding="12,10"
                               FontSize="14"
                               BorderBrush="#E0E0E0"
                               BorderThickness="2"
                               Background="White"/>
                </StackPanel>

                <!-- ملاحظات إضافية -->
                <StackPanel Margin="0,0,0,10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="📋" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="ملاحظات (اختياري)" FontSize="14" FontWeight="SemiBold"
                                  Foreground="#333333" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBox x:Name="NotesTextBox"
                            Height="60" Padding="12,10"
                            FontSize="14"
                            BorderBrush="#E0E0E0"
                            BorderThickness="2"
                            Background="White"
                            Foreground="#333333"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2"
               Background="White"
               BorderThickness="0,1,0,0"
               BorderBrush="#E0E0E0"
               Padding="20,15">

            <StackPanel Orientation="Horizontal"
                       HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="💾 حفظ"
                       Background="#28A745"
                       Foreground="White"
                       BorderThickness="0"
                       Width="120"
                       Height="40"
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Click="SaveButton_Click"
                       IsDefault="True">
                    <Button.Effect>
                        <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                    </Button.Effect>
                </Button>

                <Button x:Name="CancelButton"
                       Content="❌ إلغاء"
                       Background="White"
                       Foreground="#666666"
                       BorderBrush="#E0E0E0"
                       BorderThickness="2"
                       Width="120"
                       Height="40"
                       Margin="10,0"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Click="CancelButton_Click"
                       IsCancel="True">
                    <Button.Effect>
                        <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                    </Button.Effect>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
