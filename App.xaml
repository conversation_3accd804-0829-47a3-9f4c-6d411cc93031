﻿<Application x:Class="Archif.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Archif"
             StartupUri="MainWindow.xaml"
             DispatcherUnhandledException="Application_DispatcherUnhandledException">
    <Application.Resources>
        <!-- تضمين الأنماط الموحدة -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- خطوط عربية بسيطة -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Background" Value="#F5F5F5"/>
            </Style>
            <Style TargetType="{x:Type TextBlock}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>
            <Style TargetType="{x:Type Button}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
