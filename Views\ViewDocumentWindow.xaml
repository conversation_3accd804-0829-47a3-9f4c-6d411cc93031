<Window x:Class="Archif.Views.ViewDocumentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="عرض تفاصيل الوثيقة"
        Width="900" Height="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <Border Grid.Row="0" Margin="0,0,0,20" Padding="20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📄" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="WindowTitleText" Text="تفاصيل الوثيقة"
                          FontSize="20" FontWeight="Bold" VerticalAlignment="Center"
                          Foreground="#333333"/>
            </StackPanel>
        </Border>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- معلومات الوثيقة الأساسية -->
                <Border Margin="0,0,0,20" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="📋" FontSize="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="المعلومات الأساسية"
                                      FontSize="16" FontWeight="Bold"
                                      VerticalAlignment="Center" Foreground="#333333"/>
                        </StackPanel>

                        <!-- الحقول -->
                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- رقم الوثيقة -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="رقم الوثيقة" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="DocumentNumberText" Text="" FontSize="14"/>
                                </Border>
                            </StackPanel>

                            <!-- تاريخ الوثيقة -->
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,15">
                                <TextBlock Text="تاريخ الوثيقة" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="DocumentDateText" Text="" FontSize="14"/>
                                </Border>
                            </StackPanel>

                            <!-- نوع الوثيقة -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="نوع الوثيقة" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="DocumentTypeText" Text="" FontSize="14"/>
                                </Border>
                            </StackPanel>

                            <!-- تسلسل الحفظ -->
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,15">
                                <TextBlock Text="تسلسل الحفظ" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="ArchiveSequenceText" Text="" FontSize="14" Foreground="Red"/>
                                </Border>
                            </StackPanel>

                            <!-- القسم -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,15">
                                <TextBlock Text="القسم" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="DepartmentText" Text="" FontSize="14"/>
                                </Border>
                            </StackPanel>

                            <!-- الضبارة -->
                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,15">
                                <TextBlock Text="الضبارة" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="FolderText" Text="" FontSize="14"/>
                                </Border>
                            </StackPanel>

                            <!-- الجهة -->
                            <StackPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,15">
                                <TextBlock Text="الجهة" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="OrganizationText" Text="" FontSize="14"/>
                                </Border>
                            </StackPanel>

                            <!-- الموضوع -->
                            <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,15">
                                <TextBlock Text="الموضوع" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="SubjectText" Text="" FontSize="14" TextWrapping="Wrap"/>
                                </Border>
                            </StackPanel>

                            <!-- تاريخ الإنشاء -->
                            <StackPanel Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,0">
                                <TextBlock Text="تاريخ الإنشاء" FontWeight="Bold" Foreground="#666666" Margin="0,0,0,5"/>
                                <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                                    <TextBlock x:Name="CreatedDateText" Text="" FontSize="12" Foreground="#999999"/>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!-- قسم المرفقات -->
                <Border Margin="0,0,0,20" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان المرفقات -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="📎" FontSize="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="المرفقات والملفات"
                                      FontSize="16" FontWeight="Bold"
                                      VerticalAlignment="Center" Foreground="#333333"/>
                            <TextBlock x:Name="AttachmentsCountText" Text="(0 ملف)"
                                      FontSize="14" Foreground="#666666"
                                      VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>

                        <!-- قائمة المرفقات -->
                        <ListBox Grid.Row="1" x:Name="AttachmentsListBox"
                                Background="Transparent" BorderThickness="0"
                                MinHeight="100" MaxHeight="300">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,2" Padding="15"
                                           Background="#F8F9FA" BorderBrush="#E0E0E0"
                                           BorderThickness="1" CornerRadius="6">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="{Binding FileIcon}" FontSize="24"
                                                      VerticalAlignment="Center" Margin="0,0,15,0"/>

                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding FileName}" FontWeight="Bold" FontSize="14"
                                                          Foreground="#333333"/>
                                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                                    <TextBlock Text="{Binding TypeDisplay}" FontSize="12"
                                                              Foreground="#666666" Margin="0,0,15,0"/>
                                                    <TextBlock Text="{Binding FileSizeFormatted}" FontSize="12"
                                                              Foreground="#666666" Margin="0,0,15,0"/>
                                                    <TextBlock Text="{Binding CreatedDateFormatted}" FontSize="12"
                                                              Foreground="#999999"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="👁️" ToolTip="معاينة"
                                                   Background="#4A90E2" Foreground="White"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   Margin="5" Click="PreviewAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="3" Content="📂" ToolTip="فتح"
                                                   Background="#28A745" Foreground="White"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   Margin="5" Click="OpenAttachment_Click" Tag="{Binding}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Grid>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار النافذة -->
        <Border Grid.Row="2" Margin="0,20,0,0" Padding="20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="EditDocumentButton"
                       Background="#28A745" Foreground="White"
                       BorderThickness="0" Padding="20,10"
                       Margin="10" Click="EditDocumentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="تعديل الوثيقة" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>

                <Button x:Name="PrintDocumentButton"
                       Background="#6C757D" Foreground="White"
                       BorderThickness="0" Padding="20,10"
                       Margin="10" Click="PrintDocumentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🖨️" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="طباعة" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>

                <Button x:Name="CloseButton"
                       Background="#DC3545" Foreground="White"
                       BorderThickness="0" Padding="20,10"
                       Margin="10" Click="CloseButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="إغلاق" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
