<UserControl x:Class="Archif.Views.DepartmentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI, Tahoma, Arial">

    <UserControl.Resources>
        <!-- Converter للحالة النشطة -->
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- Converter لألوان الحالة -->
        <Style x:Key="StatusBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Background" Value="#DC3545"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Converter لنص الحالة -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Text" Value="نشط"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Text" Value="غير نشط"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإحصائيات -->
        <Border Grid.Row="0" Margin="0,0,0,20" Padding="25,20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🏢" FontSize="32" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الأقسام"
                              FontSize="24" FontWeight="Bold"
                              Margin="5,0,0,0" VerticalAlignment="Center"
                              Foreground="#333333"/>
                </StackPanel>

                <!-- الإحصائيات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Padding="20,15" BorderBrush="#4A90E2" BorderThickness="1" CornerRadius="10" Margin="5,0">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#4A90E2" Offset="0"/>
                                <GradientStop Color="#357ABD" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock x:Name="TotalDepartmentsText" Text="0"
                                      FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock Text="إجمالي الأقسام"
                                      FontSize="12" HorizontalAlignment="Center"
                                      Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <Border Padding="20,15" BorderBrush="#28A745" BorderThickness="1" CornerRadius="10" Margin="5,0">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#28A745" Offset="0"/>
                                <GradientStop Color="#1E7E34" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock x:Name="TotalDocumentsText" Text="0"
                                      FontSize="24" FontWeight="Bold"
                                      HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock Text="إجمالي الوثائق"
                                      FontSize="12" HorizontalAlignment="Center"
                                      Foreground="White"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- شريط الأدوات والبحث -->
        <Border Grid.Row="1" Margin="0,0,0,20" Padding="20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- زر إضافة قسم -->
                <Button x:Name="AddDepartmentButton"
                       Background="#28A745" Foreground="White"
                       BorderThickness="0" Padding="20,12"
                       Click="AddDepartmentButton_Click">
                    <Button.Effect>
                        <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                    </Button.Effect>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="18" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة قسم جديد" FontSize="14" FontWeight="Bold" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>

                <!-- البحث والفلاتر -->
                <StackPanel Grid.Column="1" Orientation="Horizontal"
                           HorizontalAlignment="Center" Margin="20,0">
                    <Grid>
                        <TextBox x:Name="SearchTextBox"
                                Width="250" Height="35" Padding="10,8"
                                BorderBrush="#E0E0E0" BorderThickness="1"
                                TextChanged="SearchTextBox_TextChanged"/>
                        <TextBlock x:Name="SearchPlaceholder"
                                  Text="البحث في الأقسام..."
                                  Foreground="#999999"
                                  IsHitTestVisible="False"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Left"
                                  Margin="15,0,0,0"/>
                    </Grid>

                    <ComboBox x:Name="StatusFilterComboBox"
                             Width="120" Height="35" Padding="10,8"
                             BorderBrush="#E0E0E0" BorderThickness="1"
                             Margin="10,0,0,0"
                             SelectionChanged="StatusFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="الكل" Tag="All"/>
                        <ComboBoxItem Content="نشط" Tag="Active"/>
                        <ComboBoxItem Content="غير نشط" Tag="Inactive"/>
                    </ComboBox>
                </StackPanel>

                <!-- زر التحديث -->
                <Button Grid.Column="2" x:Name="RefreshButton"
                       Background="White" Foreground="#666666"
                       BorderBrush="#E0E0E0" BorderThickness="1"
                       Padding="15,10" Click="RefreshButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Text="تحديث" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- قائمة الأقسام فقط -->
        <ScrollViewer Grid.Row="3" VerticalScrollBarVisibility="Auto"
                     HorizontalScrollBarVisibility="Disabled">
            <ItemsControl x:Name="DepartmentsItemsControl">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Width="350" MinHeight="320"
                               Background="White"
                               CornerRadius="12" Padding="20" Margin="10"
                               BorderBrush="#E0E0E0" BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="3" BlurRadius="10" Opacity="0.1"/>
                            </Border.Effect>
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="BorderBrush" Value="#4A90E2"/>
                                            <Setter Property="BorderThickness" Value="2"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- أيقونة وعنوان القسم -->
                                <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
                                    <Border Background="#4A90E2" CornerRadius="25" Width="50" Height="50"
                                           HorizontalAlignment="Center" Margin="0,0,0,10">
                                        <TextBlock Text="🏢" FontSize="24" Foreground="White"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="{Binding Name}" FontSize="18" FontWeight="Bold"
                                              HorizontalAlignment="Center" Foreground="#333333"
                                              TextWrapping="Wrap" TextAlignment="Center"/>
                                </StackPanel>

                                <!-- إحصائيات القسم -->
                                <Grid Grid.Row="1" Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- عدد الضبائر -->
                                    <Border Grid.Column="0" Padding="15,10" Background="#E8F5E8"
                                           BorderBrush="#28A745" BorderThickness="1" CornerRadius="10"
                                           Margin="0,0,5,0">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="📂" FontSize="20" HorizontalAlignment="Center"
                                                      Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding FoldersCount}" FontSize="20" FontWeight="Bold"
                                                      HorizontalAlignment="Center" Foreground="#28A745"/>
                                            <TextBlock Text="ضبارة" FontSize="12" HorizontalAlignment="Center"
                                                      Foreground="#28A745"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- عدد الوثائق -->
                                    <Border Grid.Column="1" Padding="15,10" Background="#FFF3E0"
                                           BorderBrush="#FF9800" BorderThickness="1" CornerRadius="10"
                                           Margin="5,0,0,0">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="📄" FontSize="20" HorizontalAlignment="Center"
                                                      Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding DocumentsCount}" FontSize="20" FontWeight="Bold"
                                                      HorizontalAlignment="Center" Foreground="#FF9800"/>
                                            <TextBlock Text="وثيقة" FontSize="12" HorizontalAlignment="Center"
                                                      Foreground="#FF9800"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- تاريخ الإنشاء والحالة -->
                                <StackPanel Grid.Row="2" HorizontalAlignment="Center" Margin="0,0,0,20">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                                        <TextBlock Text="📅" FontSize="14" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding CreatedDate, StringFormat='تاريخ الإنشاء: {0:dd/MM/yyyy}'}"
                                                  FontSize="12" Foreground="#666666" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Border Style="{StaticResource StatusBorderStyle}"
                                           CornerRadius="15" Padding="12,6" HorizontalAlignment="Center">
                                        <TextBlock Style="{StaticResource StatusTextStyle}"
                                                  FontSize="11" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                </StackPanel>

                                <!-- أزرار العمليات -->
                                <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="👁️ فتح القسم" ToolTip="عرض تفاصيل القسم والضبائر"
                                           Background="#17A2B8" Foreground="White"
                                           BorderThickness="0" Padding="15,10"
                                           Margin="5" FontWeight="Bold"
                                           Click="OpenDepartmentButton_Click"
                                           Tag="{Binding}">
                                        <Button.Effect>
                                            <DropShadowEffect Color="#17A2B8" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                        </Button.Effect>
                                    </Button>

                                    <Button Content="📝" ToolTip="تعديل القسم"
                                           Background="#4A90E2" Foreground="White"
                                           BorderThickness="0" Padding="12,10" Margin="5"
                                           Click="EditDepartmentButton_Click"
                                           Tag="{Binding}">
                                        <Button.Effect>
                                            <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                        </Button.Effect>
                                    </Button>

                                    <Button Content="🗑️" ToolTip="حذف القسم"
                                           Background="#DC3545" Foreground="White"
                                           BorderThickness="0" Padding="12,10" Margin="5"
                                           Click="DeleteDepartmentButton_Click"
                                           Tag="{Binding}">
                                        <Button.Effect>
                                            <DropShadowEffect Color="#DC3545" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                        </Button.Effect>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
