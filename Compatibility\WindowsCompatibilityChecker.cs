using System;
using System.Runtime.InteropServices;
using Microsoft.Win32;
using System.Management;
using System.Diagnostics;

namespace Archif.Compatibility
{
    /// <summary>
    /// فاحص التوافق مع أنظمة Windows المختلفة
    /// </summary>
    public static class WindowsCompatibilityChecker
    {
        public enum WindowsVersion
        {
            Unknown,
            Windows7,
            Windows8,
            Windows81,
            Windows10,
            Windows11,
            WindowsServer
        }

        public class SystemRequirements
        {
            public bool IsCompatible { get; set; }
            public WindowsVersion WindowsVersion { get; set; }
            public string WindowsVersionString { get; set; } = "";
            public bool HasDotNetFramework48 { get; set; }
            public bool HasDotNet6Runtime { get; set; }
            public long AvailableMemoryMB { get; set; }
            public long TotalMemoryMB { get; set; }
            public string ProcessorInfo { get; set; } = "";
            public List<string> MissingRequirements { get; set; } = new();
            public List<string> Recommendations { get; set; } = new();
        }

        /// <summary>
        /// فحص شامل لمتطلبات النظام
        /// </summary>
        public static SystemRequirements CheckSystemRequirements()
        {
            var requirements = new SystemRequirements();

            try
            {
                // فحص إصدار Windows
                requirements.WindowsVersion = GetWindowsVersion();
                requirements.WindowsVersionString = GetWindowsVersionString();

                // فحص .NET Framework
                requirements.HasDotNetFramework48 = CheckDotNetFramework48();
                requirements.HasDotNet6Runtime = CheckDotNet6Runtime();

                // فحص الذاكرة
                GetMemoryInfo(requirements);

                // فحص المعالج
                requirements.ProcessorInfo = GetProcessorInfo();

                // تحديد التوافق
                DetermineCompatibility(requirements);
            }
            catch (Exception ex)
            {
                requirements.IsCompatible = false;
                requirements.MissingRequirements.Add($"خطأ في فحص النظام: {ex.Message}");
            }

            return requirements;
        }

        private static WindowsVersion GetWindowsVersion()
        {
            try
            {
                var version = Environment.OSVersion.Version;
                var productName = GetWindowsProductName();

                if (productName.Contains("Server"))
                    return WindowsVersion.WindowsServer;

                return version.Major switch
                {
                    6 when version.Minor == 1 => WindowsVersion.Windows7,
                    6 when version.Minor == 2 => WindowsVersion.Windows8,
                    6 when version.Minor == 3 => WindowsVersion.Windows81,
                    10 when version.Build < 22000 => WindowsVersion.Windows10,
                    10 when version.Build >= 22000 => WindowsVersion.Windows11,
                    _ => WindowsVersion.Unknown
                };
            }
            catch
            {
                return WindowsVersion.Unknown;
            }
        }

        private static string GetWindowsVersionString()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");
                var productName = key?.GetValue("ProductName")?.ToString() ?? "Unknown";
                var buildNumber = key?.GetValue("CurrentBuild")?.ToString() ?? "Unknown";
                return $"{productName} (Build {buildNumber})";
            }
            catch
            {
                return Environment.OSVersion.ToString();
            }
        }

        private static string GetWindowsProductName()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");
                return key?.GetValue("ProductName")?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        private static bool CheckDotNetFramework48()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\");
                var release = key?.GetValue("Release");
                return release != null && (int)release >= 461808; // .NET Framework 4.8
            }
            catch
            {
                return false;
            }
        }

        private static bool CheckDotNet6Runtime()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "dotnet",
                        Arguments = "--list-runtimes",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                return output.Contains("Microsoft.WindowsDesktop.App 6.0") ||
                       output.Contains("Microsoft.NETCore.App 6.0");
            }
            catch
            {
                return false;
            }
        }

        private static void GetMemoryInfo(SystemRequirements requirements)
        {
            try
            {
                var memoryStatus = new MEMORYSTATUSEX();
                memoryStatus.dwLength = (uint)Marshal.SizeOf(memoryStatus);
                
                if (GlobalMemoryStatusEx(ref memoryStatus))
                {
                    requirements.TotalMemoryMB = (long)(memoryStatus.ullTotalPhys / 1024 / 1024);
                    requirements.AvailableMemoryMB = (long)(memoryStatus.ullAvailPhys / 1024 / 1024);
                }
                else
                {
                    // Fallback method
                    requirements.TotalMemoryMB = GC.GetTotalMemory(false) / 1024 / 1024;
                    requirements.AvailableMemoryMB = requirements.TotalMemoryMB;
                }
            }
            catch
            {
                requirements.TotalMemoryMB = 0;
                requirements.AvailableMemoryMB = 0;
            }
        }

        private static string GetProcessorInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT Name FROM Win32_Processor");
                foreach (ManagementObject obj in searcher.Get())
                {
                    return obj["Name"]?.ToString() ?? "Unknown Processor";
                }
                return "Unknown Processor";
            }
            catch
            {
                return $"{Environment.ProcessorCount} Core(s)";
            }
        }

        private static void DetermineCompatibility(SystemRequirements requirements)
        {
            requirements.IsCompatible = true;

            // فحص إصدار Windows
            if (requirements.WindowsVersion == WindowsVersion.Unknown)
            {
                requirements.IsCompatible = false;
                requirements.MissingRequirements.Add("إصدار Windows غير مدعوم");
            }
            else if (requirements.WindowsVersion == WindowsVersion.Windows7)
            {
                requirements.Recommendations.Add("Windows 7 مدعوم جزئياً - يُنصح بالترقية إلى Windows 10 أو أحدث");
                
                if (!requirements.HasDotNetFramework48)
                {
                    requirements.IsCompatible = false;
                    requirements.MissingRequirements.Add(".NET Framework 4.8 مطلوب لـ Windows 7");
                }
            }

            // فحص .NET Runtime
            if (!requirements.HasDotNet6Runtime && requirements.WindowsVersion != WindowsVersion.Windows7)
            {
                requirements.MissingRequirements.Add(".NET 6.0 Runtime مطلوب");
                requirements.Recommendations.Add("يمكن تحميل .NET 6.0 Runtime من موقع Microsoft");
            }

            // فحص الذاكرة
            if (requirements.TotalMemoryMB < 2048) // أقل من 2 GB
            {
                requirements.IsCompatible = false;
                requirements.MissingRequirements.Add("الحد الأدنى للذاكرة: 2 GB");
            }
            else if (requirements.TotalMemoryMB < 4096) // أقل من 4 GB
            {
                requirements.Recommendations.Add("يُنصح بـ 4 GB ذاكرة أو أكثر للأداء الأمثل");
            }

            // فحص المساحة المتاحة
            if (requirements.AvailableMemoryMB < 512) // أقل من 512 MB متاحة
            {
                requirements.Recommendations.Add("الذاكرة المتاحة منخفضة - يُنصح بإغلاق التطبيقات الأخرى");
            }
        }

        /// <summary>
        /// إنشاء تقرير توافق مفصل
        /// </summary>
        public static string GenerateCompatibilityReport(SystemRequirements requirements)
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("=== تقرير توافق نظام الأرشفة الإلكترونية ===");
            report.AppendLine($"تاريخ الفحص: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            report.AppendLine("معلومات النظام:");
            report.AppendLine($"  - نظام التشغيل: {requirements.WindowsVersionString}");
            report.AppendLine($"  - المعالج: {requirements.ProcessorInfo}");
            report.AppendLine($"  - إجمالي الذاكرة: {requirements.TotalMemoryMB:N0} MB");
            report.AppendLine($"  - الذاكرة المتاحة: {requirements.AvailableMemoryMB:N0} MB");
            report.AppendLine();
            
            report.AppendLine("متطلبات البرمجيات:");
            report.AppendLine($"  - .NET Framework 4.8: {(requirements.HasDotNetFramework48 ? "✓ متوفر" : "✗ غير متوفر")}");
            report.AppendLine($"  - .NET 6.0 Runtime: {(requirements.HasDotNet6Runtime ? "✓ متوفر" : "✗ غير متوفر")}");
            report.AppendLine();
            
            report.AppendLine($"حالة التوافق: {(requirements.IsCompatible ? "✓ متوافق" : "✗ غير متوافق")}");
            
            if (requirements.MissingRequirements.Any())
            {
                report.AppendLine();
                report.AppendLine("المتطلبات المفقودة:");
                foreach (var req in requirements.MissingRequirements)
                {
                    report.AppendLine($"  - {req}");
                }
            }
            
            if (requirements.Recommendations.Any())
            {
                report.AppendLine();
                report.AppendLine("التوصيات:");
                foreach (var rec in requirements.Recommendations)
                {
                    report.AppendLine($"  - {rec}");
                }
            }
            
            return report.ToString();
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct MEMORYSTATUSEX
        {
            public uint dwLength;
            public uint dwMemoryLoad;
            public ulong ullTotalPhys;
            public ulong ullAvailPhys;
            public ulong ullTotalPageFile;
            public ulong ullAvailPageFile;
            public ulong ullTotalVirtual;
            public ulong ullAvailVirtual;
            public ulong ullAvailExtendedVirtual;
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool GlobalMemoryStatusEx(ref MEMORYSTATUSEX lpBuffer);
    }
}
