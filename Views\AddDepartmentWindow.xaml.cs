using System.Windows;
using System.Windows.Controls;
using Archif.Services;
using Archif.Models;
using Archif.Helpers;
using WpfMessageBox = System.Windows.MessageBox;

namespace Archif.Views
{
    /// <summary>
    /// نافذة إضافة/تحرير قسم جديد
    /// </summary>
    public partial class AddDepartmentWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly Department? _existingDepartment;
        private readonly bool _isEditMode;

        public string DepartmentName => DepartmentNameTextBox.Text.Trim();
        public DateTime CreatedDate => CreatedDatePicker.SelectedDate ?? DateTime.Now;
        public string Notes => NotesTextBox.Text.Trim();

        public AddDepartmentWindow(DatabaseService databaseService, Department? department = null)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _existingDepartment = department;
            _isEditMode = department != null;

            InitializeForm();
        }

        private void InitializeForm()
        {
            try
            {
                // تعيين العنوان حسب الوضع
                Title = _isEditMode ? "تعديل القسم" : "إضافة قسم جديد";

                // تعيين التاريخ الحالي كافتراضي
                CreatedDatePicker.SelectedDate = DateTime.Now;

                // إذا كان في وضع التعديل، املأ البيانات
                if (_isEditMode && _existingDepartment != null)
                {
                    DepartmentNameTextBox.Text = _existingDepartment.Name;
                    CreatedDatePicker.SelectedDate = _existingDepartment.CreatedDate;
                }

                // تركيز على حقل الاسم
                DepartmentNameTextBox.Focus();
                DepartmentNameTextBox.SelectAll();

                // ربط أحداث التحقق
                DepartmentNameTextBox.TextChanged += DepartmentNameTextBox_TextChanged;
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DepartmentNameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateDepartmentName();
        }

        private bool ValidateDepartmentName()
        {
            var name = DepartmentNameTextBox.Text.Trim();

            if (string.IsNullOrWhiteSpace(name))
            {
                NameValidationText.Text = "يرجى إدخال اسم القسم";
                NameValidationText.Visibility = Visibility.Visible;
                DepartmentNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else if (name.Length < 2)
            {
                NameValidationText.Text = "اسم القسم يجب أن يكون أكثر من حرف واحد";
                NameValidationText.Visibility = Visibility.Visible;
                DepartmentNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else if (name.Length > 200)
            {
                NameValidationText.Text = "اسم القسم طويل جداً (الحد الأقصى 200 حرف)";
                NameValidationText.Visibility = Visibility.Visible;
                DepartmentNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            else
            {
                NameValidationText.Visibility = Visibility.Collapsed;
                DepartmentNameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                return true;
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateDepartmentName())
                {
                    DepartmentNameTextBox.Focus();
                    return;
                }

                // تعطيل الأزرار أثناء الحفظ
                SaveButton.IsEnabled = false;
                SaveButton.Content = "⏳ جاري الحفظ...";

                if (_isEditMode && _existingDepartment != null)
                {
                    // تحديث القسم الموجود
                    _existingDepartment.Name = DepartmentName;
                    await _databaseService.UpdateDepartmentAsync(_existingDepartment);

                    WpfMessageBox.Show("تم تعديل القسم بنجاح", "نجح التعديل",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // إضافة قسم جديد
                    var newDepartment = new Department
                    {
                        Name = DepartmentName,
                        CreatedDate = CreatedDate,
                        IsActive = true
                    };

                    await _databaseService.AddDepartmentAsync(newDepartment.Name);

                    WpfMessageBox.Show("تم إضافة القسم بنجاح", "نجح الإضافة",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "حفظ القسم");
            }
            finally
            {
                // إعادة تفعيل الأزرار
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 حفظ";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
