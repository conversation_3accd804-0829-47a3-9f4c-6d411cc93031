<Window x:Class="Archif.Views.DepartmentDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تفاصيل القسم" Height="700" Width="1000"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Converter للحالة النشطة -->
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- Converter لألوان الحالة -->
        <Style x:Key="StatusBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Background" Value="#DC3545"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Converter لنص الحالة -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Text" Value="نشط"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsActive}" Value="False">
                    <Setter Property="Text" Value="غير نشط"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة ومعلومات القسم -->
        <Border Grid.Row="0" Margin="0,0,0,20" Padding="25" CornerRadius="12">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#4A90E2" Offset="0"/>
                    <GradientStop Color="#357ABD" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.3"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- أيقونة القسم -->
                <Border Grid.Column="0" Background="White" CornerRadius="30" Width="60" Height="60"
                       VerticalAlignment="Center" Margin="0,0,20,0">
                    <TextBlock Text="🏢" FontSize="30" Foreground="#4A90E2"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>

                <!-- معلومات القسم -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock x:Name="DepartmentNameText" Text="اسم القسم"
                              FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Text="📅" FontSize="14" Foreground="White" Margin="0,0,5,0"/>
                        <TextBlock x:Name="CreatedDateText" Text="تاريخ الإنشاء"
                                  FontSize="14" Foreground="White"/>
                    </StackPanel>
                </StackPanel>

                <!-- حالة القسم -->
                <Border Grid.Column="2" x:Name="StatusBorder" CornerRadius="20" Padding="15,8"
                       VerticalAlignment="Center" Background="#28A745">
                    <TextBlock x:Name="StatusText" Text="نشط" FontSize="14"
                              Foreground="White" FontWeight="Bold"/>
                </Border>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- عدد الضبائر -->
            <Border Grid.Column="0" Padding="20,15" Background="#E8F5E8"
                   BorderBrush="#28A745" BorderThickness="2" CornerRadius="12"
                   Margin="0,0,10,0">
                <Border.Effect>
                    <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.2"/>
                </Border.Effect>
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📂" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="FoldersCountText" Text="0" FontSize="28" FontWeight="Bold"
                              HorizontalAlignment="Center" Foreground="#28A745"/>
                    <TextBlock Text="ضبارة" FontSize="14" HorizontalAlignment="Center"
                              Foreground="#28A745" FontWeight="Bold"/>
                </StackPanel>
            </Border>

            <!-- عدد الوثائق -->
            <Border Grid.Column="1" Padding="20,15" Background="#FFF3E0"
                   BorderBrush="#FF9800" BorderThickness="2" CornerRadius="12"
                   Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#FF9800" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.2"/>
                </Border.Effect>
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📄" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="DocumentsCountText" Text="0" FontSize="28" FontWeight="Bold"
                              HorizontalAlignment="Center" Foreground="#FF9800"/>
                    <TextBlock Text="وثيقة" FontSize="14" HorizontalAlignment="Center"
                              Foreground="#FF9800" FontWeight="Bold"/>
                </StackPanel>
            </Border>

            <!-- الوثائق الواردة -->
            <Border Grid.Column="2" Padding="20,15" Background="#E3F2FD"
                   BorderBrush="#2196F3" BorderThickness="2" CornerRadius="12"
                   Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#2196F3" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.2"/>
                </Border.Effect>
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📥" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="IncomingDocsText" Text="0" FontSize="28" FontWeight="Bold"
                              HorizontalAlignment="Center" Foreground="#2196F3"/>
                    <TextBlock Text="واردة" FontSize="14" HorizontalAlignment="Center"
                              Foreground="#2196F3" FontWeight="Bold"/>
                </StackPanel>
            </Border>

            <!-- الوثائق الصادرة -->
            <Border Grid.Column="3" Padding="20,15" Background="#FCE4EC"
                   BorderBrush="#E91E63" BorderThickness="2" CornerRadius="12"
                   Margin="10,0,0,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E91E63" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.2"/>
                </Border.Effect>
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📤" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="OutgoingDocsText" Text="0" FontSize="28" FontWeight="Bold"
                              HorizontalAlignment="Center" Foreground="#E91E63"/>
                    <TextBlock Text="صادرة" FontSize="14" HorizontalAlignment="Center"
                              Foreground="#E91E63" FontWeight="Bold"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- قائمة الضبائر -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0"
               BorderThickness="1" CornerRadius="12" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.2"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان قسم الضبائر -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                    <TextBlock Text="📂" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="ضبائر القسم" FontSize="20" FontWeight="Bold"
                              VerticalAlignment="Center" Foreground="#333333"/>
                </StackPanel>

                <!-- شريط الأدوات -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
                    <Button x:Name="AddFolderButton"
                           Background="#28A745" Foreground="White"
                           BorderThickness="0" Padding="15,10"
                           Click="AddFolderButton_Click">
                        <Button.Effect>
                            <DropShadowEffect Color="#28A745" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                        </Button.Effect>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة ضبارة جديدة" FontSize="14" FontWeight="Bold"/>
                        </StackPanel>
                    </Button>

                    <Grid Margin="20,0,0,0">
                        <TextBox x:Name="SearchFoldersTextBox" Width="250" Height="35" Padding="10,8"
                                BorderBrush="#E0E0E0" BorderThickness="1"
                                TextChanged="SearchFoldersTextBox_TextChanged"/>
                        <TextBlock x:Name="SearchFoldersPlaceholder"
                                  Text="البحث في الضبائر..."
                                  Foreground="#999999"
                                  IsHitTestVisible="False"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Left"
                                  Margin="15,0,0,0"/>
                    </Grid>
                </StackPanel>

                <!-- قائمة الضبائر -->
                <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                    <ItemsControl x:Name="FoldersItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Margin="0,0,0,15" Padding="20"
                                       Background="#F8F9FA" BorderBrush="#E0E0E0"
                                       BorderThickness="1" CornerRadius="10">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                    </Border.Effect>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- أيقونة الضبارة -->
                                        <Border Grid.Column="0" Background="#4A90E2" CornerRadius="20"
                                               Width="40" Height="40" VerticalAlignment="Center" Margin="0,0,15,0">
                                            <TextBlock Text="📂" FontSize="18" Foreground="White"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- معلومات الضبارة -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" FontSize="16" FontWeight="Bold"
                                                      Foreground="#333333"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                                <TextBlock Text="📄" FontSize="12" Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding DocumentsCount, StringFormat=\{0\} وثيقة}"
                                                          FontSize="12" Foreground="#666666"/>
                                                <TextBlock Text=" • " FontSize="12" Foreground="#666666" Margin="5,0"/>
                                                <TextBlock Text="{Binding CreatedDate, StringFormat='تاريخ الإنشاء: {0:dd/MM/yyyy}'}"
                                                          FontSize="12" Foreground="#666666"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- عدد الوثائق -->
                                        <Border Grid.Column="2" Background="#FF9800" CornerRadius="15"
                                               Padding="10,5" VerticalAlignment="Center" Margin="10,0">
                                            <TextBlock Text="{Binding DocumentsCount}" FontSize="14" FontWeight="Bold"
                                                      Foreground="White" HorizontalAlignment="Center"/>
                                        </Border>

                                        <!-- أزرار العمليات -->
                                        <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center">
                                            <Button Content="👁️" ToolTip="عرض الوثائق"
                                                   Background="#17A2B8" Foreground="White"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   Margin="5,0"
                                                   Click="ViewFolderDocumentsButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#17A2B8" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                            <Button Content="📝" ToolTip="تعديل الضبارة"
                                                   Background="#4A90E2" Foreground="White"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   Margin="5,0"
                                                   Click="EditFolderButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                            <Button Content="🗑️" ToolTip="حذف الضبارة"
                                                   Background="#DC3545" Foreground="White"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   Margin="5,0"
                                                   Click="DeleteFolderButton_Click"
                                                   Tag="{Binding}">
                                                <Button.Effect>
                                                    <DropShadowEffect Color="#DC3545" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                                                </Button.Effect>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- أزرار النافذة -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="EditDepartmentButton" Content="📝 تعديل القسم"
                   Background="#4A90E2" Foreground="White"
                   BorderThickness="0" Padding="20,12"
                   Margin="10,0" FontWeight="Bold"
                   Click="EditDepartmentButton_Click">
                <Button.Effect>
                    <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                </Button.Effect>
            </Button>

            <Button x:Name="CloseButton" Content="🚪 إغلاق"
                   Background="#6C757D" Foreground="White"
                   BorderThickness="0" Padding="20,12"
                   Margin="10,0" FontWeight="Bold"
                   Click="CloseButton_Click">
                <Button.Effect>
                    <DropShadowEffect Color="#6C757D" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                </Button.Effect>
            </Button>
        </StackPanel>
    </Grid>
</Window>
