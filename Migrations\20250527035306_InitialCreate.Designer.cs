﻿// <auto-generated />
using System;
using Archif.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Archif.Migrations
{
    [DbContext(typeof(ArchifDbContext))]
    [Migration("20250527035306_InitialCreate")]
    partial class InitialCreate
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "6.0.25");

            modelBuilder.Entity("Archif.Models.Attachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("DocumentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.ToTable("Attachments");
                });

            modelBuilder.Entity("Archif.Models.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Departments");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5097),
                            IsActive = true,
                            Name = "الإدارة العامة"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5103),
                            IsActive = true,
                            Name = "الشؤون المالية"
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5108),
                            IsActive = true,
                            Name = "الموارد البشرية"
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(5113),
                            IsActive = true,
                            Name = "الشؤون القانونية"
                        });
                });

            modelBuilder.Entity("Archif.Models.Document", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArchiveSequence")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DocumentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("DocumentNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("FolderId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FromTo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("OrganizationId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FolderId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("Archif.Models.DocumentHyperlink", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("DocumentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.ToTable("DocumentHyperlinks");
                });

            modelBuilder.Entity("Archif.Models.Folder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Folders");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9580),
                            DepartmentId = 1,
                            IsActive = true,
                            Name = "المراسلات العامة"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9584),
                            DepartmentId = 1,
                            IsActive = true,
                            Name = "القرارات الإدارية"
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9586),
                            DepartmentId = 2,
                            IsActive = true,
                            Name = "الميزانية"
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9588),
                            DepartmentId = 2,
                            IsActive = true,
                            Name = "المصروفات"
                        },
                        new
                        {
                            Id = 5,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9590),
                            DepartmentId = 3,
                            IsActive = true,
                            Name = "التوظيف"
                        },
                        new
                        {
                            Id = 6,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9592),
                            DepartmentId = 4,
                            IsActive = true,
                            Name = "العقود"
                        });
                });

            modelBuilder.Entity("Archif.Models.Organization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("Organizations");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9707),
                            IsActive = true,
                            Name = "وزارة الداخلية",
                            Type = 0
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9869),
                            IsActive = true,
                            Name = "وزارة المالية",
                            Type = 0
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9872),
                            IsActive = true,
                            Name = "وزارة التعليم",
                            Type = 0
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9874),
                            IsActive = true,
                            Name = "جامعة الملك سعود",
                            Type = 2
                        },
                        new
                        {
                            Id = 5,
                            CreatedDate = new DateTime(2025, 5, 27, 6, 53, 6, 276, DateTimeKind.Local).AddTicks(9876),
                            IsActive = true,
                            Name = "شركة أرامكو السعودية",
                            Type = 1
                        });
                });

            modelBuilder.Entity("Archif.Models.Attachment", b =>
                {
                    b.HasOne("Archif.Models.Document", "Document")
                        .WithMany("Attachments")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Archif.Models.Document", b =>
                {
                    b.HasOne("Archif.Models.Department", "Department")
                        .WithMany("Documents")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Archif.Models.Folder", "Folder")
                        .WithMany("Documents")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Archif.Models.Organization", "Organization")
                        .WithMany("Documents")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Department");

                    b.Navigation("Folder");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("Archif.Models.DocumentHyperlink", b =>
                {
                    b.HasOne("Archif.Models.Document", "Document")
                        .WithMany("Hyperlinks")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Archif.Models.Folder", b =>
                {
                    b.HasOne("Archif.Models.Department", "Department")
                        .WithMany("Folders")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("Archif.Models.Department", b =>
                {
                    b.Navigation("Documents");

                    b.Navigation("Folders");
                });

            modelBuilder.Entity("Archif.Models.Document", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Hyperlinks");
                });

            modelBuilder.Entity("Archif.Models.Folder", b =>
                {
                    b.Navigation("Documents");
                });

            modelBuilder.Entity("Archif.Models.Organization", b =>
                {
                    b.Navigation("Documents");
                });
#pragma warning restore 612, 618
        }
    }
}
