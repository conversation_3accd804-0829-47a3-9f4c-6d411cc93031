<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ألوان موحدة للتطبيق -->
    <SolidColorBrush x:Key="PrimaryColor" Color="#4A90E2"/>
    <SolidColorBrush x:Key="SecondaryColor" Color="#28A745"/>
    <SolidColorBrush x:Key="DangerColor" Color="#DC3545"/>
    <SolidColorBrush x:Key="WarningColor" Color="#FFC107"/>
    <SolidColorBrush x:Key="InfoColor" Color="#17A2B8"/>
    <SolidColorBrush x:Key="LightColor" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="DarkColor" Color="#343A40"/>
    <SolidColorBrush x:Key="MutedColor" Color="#6C757D"/>

    <!-- ألوان الخلفية -->
    <SolidColorBrush x:Key="BackgroundColor" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="CardBackgroundColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="HeaderBackgroundColor" Color="#2C3E50"/>

    <!-- ألوان النصوص -->
    <SolidColorBrush x:Key="PrimaryTextColor" Color="#2C3E50"/>
    <SolidColorBrush x:Key="SecondaryTextColor" Color="#666666"/>
    <SolidColorBrush x:Key="MutedTextColor" Color="#999999"/>

    <!-- أنماط الأزرار الموحدة -->
    <Style x:Key="PrimaryButton" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="15,10"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
    </Style>

    <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background" Value="{StaticResource DangerColor}"/>
    </Style>

    <Style x:Key="InfoButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background" Value="{StaticResource InfoColor}"/>
    </Style>

    <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background" Value="{StaticResource WarningColor}"/>
        <Setter Property="Foreground" Value="{StaticResource DarkColor}"/>
    </Style>

    <!-- أنماط البطاقات الموحدة -->
    <Style x:Key="CardBorder" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBackgroundColor}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="3" BlurRadius="10" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط النصوص الموحدة -->
    <Style x:Key="HeaderText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}"/>
        <Setter Property="Margin" Value="0,0,0,15"/>
    </Style>

    <Style x:Key="SubHeaderText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <Style x:Key="BodyText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextColor}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="MutedText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource MutedTextColor}"/>
    </Style>

    <!-- أنماط حقول الإدخال الموحدة -->
    <Style x:Key="ModernTextBox" TargetType="TextBox">
        <Setter Property="Padding" Value="12,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="Background" Value="White"/>
    </Style>

    <!-- أنماط ComboBox الموحدة -->
    <Style x:Key="ModernComboBox" TargetType="ComboBox">
        <Setter Property="Padding" Value="12,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="Background" Value="White"/>
    </Style>

</ResourceDictionary>
