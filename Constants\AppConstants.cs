using System;
using System.IO;

namespace Archif.Constants
{
    /// <summary>
    /// ثوابت التطبيق المشتركة
    /// </summary>
    public static class AppConstants
    {
        /// <summary>
        /// معلومات التطبيق
        /// </summary>
        public static class AppInfo
        {
            public const string Name = "أرشيف";
            public const string Version = "2.0.0";
            public const string Description = "نظام إدارة الوثائق والأرشفة";
            public const string Company = "شركة التطوير";
        }

        /// <summary>
        /// إعدادات قاعدة البيانات
        /// </summary>
        public static class Database
        {
            public const string FileName = "archif.db";
            public const string FolderName = "Archif";
        }

        /// <summary>
        /// رسائل النظام
        /// </summary>
        public static class Messages
        {
            // رسائل النجح
            public const string AddSuccess = "تم الإضافة بنجاح";
            public const string UpdateSuccess = "تم التحديث بنجاح";
            public const string DeleteSuccess = "تم الحذف بنجاح";
            public const string SaveSuccess = "تم الحفظ بنجاح";

            // رسائل الخطأ
            public const string AddError = "خطأ في الإضافة";
            public const string UpdateError = "خطأ في التحديث";
            public const string DeleteError = "خطأ في الحذف";
            public const string LoadError = "خطأ في التحميل";
            public const string SaveError = "خطأ في الحفظ";

            // رسائل التأكيد
            public const string DeleteConfirmation = "هل أنت متأكد من الحذف؟";
            public const string SaveConfirmation = "هل تريد حفظ التغييرات؟";

            // رسائل التحذير
            public const string CannotDeleteWithData = "لا يمكن الحذف لوجود بيانات مرتبطة";
            public const string RequiredFieldsEmpty = "يرجى ملء جميع الحقول المطلوبة";
        }

        /// <summary>
        /// إعدادات واجهة المستخدم
        /// </summary>
        public static class UI
        {
            public const int DefaultPageSize = 20;
            public const int MaxSearchResults = 100;
            public const int AutoSaveInterval = 30; // بالثواني
        }

        /// <summary>
        /// أنواع الملفات المدعومة
        /// </summary>
        public static class FileTypes
        {
            public static readonly string[] Documents = { ".pdf", ".doc", ".docx", ".txt", ".rtf" };
            public static readonly string[] Images = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff" };
            public static readonly string[] Archives = { ".zip", ".rar", ".7z" };
            public static readonly string[] AllSupported =
            {
                ".pdf", ".doc", ".docx", ".txt", ".rtf",
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff",
                ".zip", ".rar", ".7z"
            };
        }

        /// <summary>
        /// حدود النظام
        /// </summary>
        public static class Limits
        {
            public const int MaxFileSize = 50 * 1024 * 1024; // 50 MB
            public const int MaxAttachmentsPerDocument = 10;
            public const int MaxNameLength = 255;
            public const int MaxDescriptionLength = 1000;
            public const int MaxSearchTextLength = 100;
        }

        /// <summary>
        /// مسارات النظام
        /// </summary>
        public static class Paths
        {
            public static readonly string AppData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            public static readonly string AppFolder = Path.Combine(AppData, Database.FolderName);
            public static readonly string DatabasePath = Path.Combine(AppFolder, Database.FileName);
            public static readonly string AttachmentsFolder = Path.Combine(AppFolder, "Attachments");
            public static readonly string BackupsFolder = Path.Combine(AppFolder, "Backups");
            public static readonly string LogsFolder = Path.Combine(AppFolder, "Logs");
        }

        /// <summary>
        /// تنسيقات التاريخ والوقت
        /// </summary>
        public static class DateFormats
        {
            public const string ShortDate = "dd/MM/yyyy";
            public const string LongDate = "dd/MM/yyyy HH:mm";
            public const string FileName = "yyyyMMdd_HHmmss";
            public const string Display = "dd MMMM yyyy";
        }
    }
}
