# نظام الأرشفة الإلكترونية - Archif

نظام احترافي لإدارة الوثائق الصادرة والواردة باستخدام WPF و .NET 6.0

## المميزات الرئيسية

### ✅ المميزات المكتملة:
- **واجهة مستخدم عربية**: تصميم كامل باللغة العربية مع دعم RTL
- **Material Design**: تصميم حديث وأنيق باستخدام Material Design
- **قاعدة بيانات SQLite**: مع Entity Framework Core لإدارة البيانات
- **النافذة الرئيسية**: مع شريط جانبي للتنقل
- **صفحة الرئيسية**: لوحة معلومات مع إحصائيات ومربع بحث متقدم
- **إدارة الأقسام**: عرض الأقسام في شكل بطاقات
- **عرض الوثائق**: جدول تفاعلي مع فلاتر متقدمة
- **إضافة الوثائق**: نموذج شامل لإدخال بيانات الوثائق
- **إدارة المرفقات**: إضافة ملفات PDF والصور
- **النسخ الاحتياطي**: إنشاء واستعادة النسخ الاحتياطية
- **البحث المتقدم**: بحث شامل في جميع حقول الوثائق

### 🔄 المميزات قيد التطوير:
- المسح الضوئي للوثائق
- عارض PDF/الصور المدمج
- تصدير التقارير إلى Excel/PDF
- إدارة متقدمة للأقسام والضبائر
- نظام الصلاحيات والمستخدمين

## متطلبات النظام

- Windows 7 أو أحدث
- .NET 6.0 Runtime
- 100 MB مساحة فارغة على القرص الصلب

## التثبيت والتشغيل

### 1. تثبيت .NET 6.0
```bash
# تحميل وتثبيت .NET 6.0 من الموقع الرسمي
https://dotnet.microsoft.com/download/dotnet/6.0
```

### 2. تشغيل المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd Archif

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

## بنية المشروع

```
Archif/
├── Models/                 # نماذج قاعدة البيانات
│   ├── Department.cs      # نموذج الأقسام
│   ├── Folder.cs          # نموذج الضبائر
│   ├── Document.cs        # نموذج الوثائق
│   └── Attachment.cs      # نموذج المرفقات
├── Data/                  # طبقة البيانات
│   └── ArchifDbContext.cs # سياق قاعدة البيانات
├── Services/              # الخدمات
│   └── DatabaseService.cs # خدمة قاعدة البيانات
├── Views/                 # واجهات المستخدم
│   ├── HomePage.xaml      # الصفحة الرئيسية
│   ├── DepartmentsPage.xaml # صفحة الأقسام
│   ├── DocumentsPage.xaml # صفحة الوثائق
│   ├── SettingsPage.xaml # صفحة الإعدادات
│   └── AddDocumentWindow.xaml # نافذة إضافة وثيقة
├── MainWindow.xaml        # النافذة الرئيسية
└── App.xaml              # تطبيق WPF
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **Departments**: الأقسام
- **Folders**: الضبائر
- **Documents**: الوثائق
- **Attachments**: المرفقات

### موقع قاعدة البيانات:
```
%AppData%\Archif\archif.db
```

## الاستخدام

### 1. الصفحة الرئيسية
- عرض إحصائيات سريعة
- بحث متقدم في الوثائق
- أزرار سريعة للوظائف الأساسية

### 2. إضافة وثيقة جديدة
- تعبئة بيانات الوثيقة (رقم، تاريخ، موضوع، إلخ)
- اختيار القسم والضبارة
- إرفاق ملفات PDF أو صور
- حفظ تلقائي مع تسلسل الحفظ

### 3. عرض الوثائق
- جدول تفاعلي لجميع الوثائق
- فلاتر حسب السنة، القسم، النوع
- النقر المزدوج لعرض التفاصيل

### 4. إدارة الأقسام
- عرض الأقسام في بطاقات
- إحصائيات لكل قسم
- إضافة وتعديل الأقسام

### 5. النسخ الاحتياطي
- إنشاء نسخة احتياطية تلقائية
- استعادة البيانات من نسخة احتياطية
- تسمية تلقائية بالتاريخ والوقت

## التقنيات المستخدمة

- **WPF**: واجهة المستخدم
- **.NET 6.0**: إطار العمل
- **Entity Framework Core**: ORM
- **SQLite**: قاعدة البيانات
- **Material Design**: تصميم الواجهة
- **MVVM Pattern**: نمط التصميم

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

## الإصدارات القادمة

### v1.1.0 (قريباً)
- [ ] المسح الضوئي المدمج
- [ ] عارض PDF/الصور
- [ ] تصدير التقارير
- [ ] نظام الصلاحيات

### v1.2.0 (مستقبلاً)
- [ ] واجهة ويب
- [ ] تطبيق الهاتف المحمول
- [ ] التزامن السحابي
- [ ] الذكاء الاصطناعي للتصنيف

---

**تم التطوير بواسطة**: فريق التطوير  
**تاريخ الإصدار**: 2024  
**الإصدار**: 1.0.0
