<Window x:Class="Archif.Views.AddDocumentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة وثيقة جديدة - نظام الأرشفة الذهبي"
        Height="800" Width="1400"
        MinHeight="700" MinWidth="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="{StaticResource BackgroundGoldBrush}">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/GoldenTheme.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان الذهبي -->
        <Border Grid.Row="0" Padding="40,25" Style="{StaticResource GoldenCardStyle}" Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Ellipse Width="35" Height="35" Fill="{StaticResource ButtonGradientBrush}"
                         VerticalAlignment="Center" Effect="{StaticResource GoldenShadowEffect}"/>
                <TextBlock Text="✨ إضافة وثيقة جديدة ✨"
                          Style="{StaticResource HeaderTextStyle}"
                          Margin="20,0,0,0" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="30,10,30,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="500"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الجانب الأيمن - حقول البيانات -->
            <Border Grid.Column="0" Margin="0,0,20,0" Style="{StaticResource GoldenCardStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📋 بيانات الوثيقة الأساسية"
                                  Style="{StaticResource SubHeaderTextStyle}"/>

                        <!-- نوع الكتاب -->
                        <TextBlock Text="📄 نوع الكتاب" Style="{StaticResource LabelTextStyle}"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5,0,20">
                            <RadioButton x:Name="OutgoingRadio"
                                       Content="📤 صادر"
                                       IsChecked="True"
                                       Margin="0,0,30,0"
                                       FontSize="14" FontWeight="SemiBold"
                                       Foreground="{StaticResource PrimaryTextBrush}"
                                       Checked="DocumentType_Changed"/>
                            <RadioButton x:Name="IncomingRadio"
                                       Content="📥 وارد"
                                       FontSize="14" FontWeight="SemiBold"
                                       Foreground="{StaticResource PrimaryTextBrush}"
                                       Checked="DocumentType_Changed"/>
                        </StackPanel>

                        <!-- التسلسل التلقائي -->
                        <TextBlock Text="🔢 التسلسل التلقائي" Style="{StaticResource LabelTextStyle}"/>
                        <TextBox x:Name="SequenceTextBox"
                                Style="{StaticResource GoldenTextBoxStyle}"
                                IsReadOnly="True"
                                Background="{StaticResource SecondaryBackgroundBrush}"/>

                        <!-- تاريخ الإدخال -->
                        <TextBlock Text="📅 تاريخ الإدخال" Style="{StaticResource LabelTextStyle}"/>
                        <DatePicker x:Name="CreatedDatePicker"
                                   Style="{StaticResource GoldenDatePickerStyle}"
                                   IsEnabled="False"
                                   Background="{StaticResource SecondaryBackgroundBrush}"/>

                        <!-- رقم الكتاب -->
                        <TextBlock Text="🔖 رقم الكتاب *" Style="{StaticResource LabelTextStyle}"/>
                        <TextBox x:Name="DocumentNumberTextBox"
                                Style="{StaticResource GoldenTextBoxStyle}"/>

                        <!-- تاريخ الكتاب -->
                        <TextBlock Text="📆 تاريخ الكتاب *" Style="{StaticResource LabelTextStyle}"/>
                        <DatePicker x:Name="DocumentDatePicker"
                                   Style="{StaticResource GoldenDatePickerStyle}"/>

                        <!-- موضوع الكتاب -->
                        <TextBlock Text="📝 موضوع الكتاب *" Style="{StaticResource LabelTextStyle}"/>
                        <TextBox x:Name="SubjectTextBox"
                                Style="{StaticResource GoldenTextBoxStyle}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"/>


                        <!-- القسم -->
                        <TextBlock Text="🏢 القسم *" Style="{StaticResource LabelTextStyle}"/>
                        <ComboBox x:Name="DepartmentComboBox"
                                 Style="{StaticResource GoldenComboBoxStyle}"
                                 SelectionChanged="DepartmentComboBox_SelectionChanged"/>

                        <!-- ضبارة الكتاب -->
                        <TextBlock Text="📁 ضبارة الكتاب *" Style="{StaticResource LabelTextStyle}"/>
                        <ComboBox x:Name="FolderComboBox"
                                 Style="{StaticResource GoldenComboBoxStyle}"/>

                        <!-- جهة الكتاب -->
                        <TextBlock Text="🏛️ جهة الكتاب (اختياري)" Style="{StaticResource LabelTextStyle}"/>
                        <ComboBox x:Name="OrganizationComboBox"
                                 Style="{StaticResource GoldenComboBoxStyle}"/>

                        <!-- تسلسل الحفظ -->
                        <TextBlock Text="📋 تسلسل الحفظ *" Style="{StaticResource LabelTextStyle}"/>
                        <TextBox x:Name="ArchiveSequenceTextBox"
                                Style="{StaticResource GoldenTextBoxStyle}"
                                Foreground="{StaticResource AccentTextBrush}"
                                TextChanged="ArchiveSequenceTextBox_TextChanged"/>
                        <TextBlock Text="💡 أدخل رقم التسلسل فقط (مثال: 1، 2، 3...)"
                                  FontSize="12"
                                  Foreground="{StaticResource SecondaryTextBrush}"
                                  HorizontalAlignment="Center"
                                  TextAlignment="Center"
                                  Margin="0,5,0,15"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- الجانب الأيسر - إدارة المرفقات والروابط -->
            <Grid Grid.Column="1" Margin="20,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- قسم المرفقات -->
                <Border Grid.Row="0" Style="{StaticResource GoldenCardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان المرفقات -->
                        <TextBlock Grid.Row="0"
                                  Text="📎 المرفقات والملفات الممسوحة ضوئياً"
                                  Style="{StaticResource SubHeaderTextStyle}"/>

                        <!-- أزرار إدارة الملفات -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal"
                                   HorizontalAlignment="Center" Margin="0,10,0,20">
                            <Button x:Name="AddFilesButton"
                                   Style="{StaticResource GoldenButtonStyle}"
                                   Click="AddFilesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📁" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة ملفات"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ScanButton"
                                   Style="{StaticResource GoldenButtonStyle}"
                                   Click="ScanButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📷" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="مسح ضوئي"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="AddFromCameraButton"
                                   Style="{StaticResource GoldenButtonStyle}"
                                   Click="AddFromCameraButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📸" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="من الكاميرا"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- قائمة الملفات المرفقة -->
                        <ListBox Grid.Row="2" x:Name="AttachmentsListBox"
                                Background="Transparent" BorderThickness="0"
                                Margin="10,0">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,5" Padding="15"
                                           Background="{StaticResource CardBackgroundBrush}"
                                           BorderBrush="{StaticResource BorderGoldBrush}"
                                           BorderThickness="2" CornerRadius="10"
                                           Effect="{StaticResource GoldenShadowEffect}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="{Binding FileIcon}" FontSize="24"
                                                      VerticalAlignment="Center" Margin="0,0,15,0"/>

                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding FileName}" FontWeight="Bold"
                                                          Foreground="{StaticResource PrimaryTextBrush}"
                                                          FontSize="14"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding TypeDisplay}" FontSize="12"
                                                              Foreground="{StaticResource SecondaryTextBrush}"
                                                              Margin="0,0,15,0"/>
                                                    <TextBlock Text="{Binding FileSizeFormatted}" FontSize="12"
                                                              Foreground="{StaticResource SecondaryTextBrush}"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="👁️" ToolTip="معاينة"
                                                   Background="Transparent"
                                                   Foreground="{StaticResource DeepGoldBrush}"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   FontSize="16" Margin="5,0"
                                                   Click="PreviewAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="3" Content="📂" ToolTip="فتح"
                                                   Background="Transparent"
                                                   Foreground="{StaticResource DeepGoldBrush}"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   FontSize="16" Margin="5,0"
                                                   Click="OpenAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="4" Content="🗑️" ToolTip="حذف"
                                                   Background="Transparent"
                                                   Foreground="{StaticResource AccentTextBrush}"
                                                   BorderThickness="0" Width="35" Height="35"
                                                   FontSize="16" Margin="5,0"
                                                   Click="RemoveAttachment_Click" Tag="{Binding}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <!-- إعدادات الماسح الضوئي -->
                        <Expander Grid.Row="3"
                                 Header="⚙️ إعدادات الماسح الضوئي"
                                 Margin="0,15,0,0"
                                 Background="Transparent"
                                 Foreground="{StaticResource AccentTextBrush}"
                                 FontWeight="SemiBold">
                            <StackPanel Margin="0,15,0,0">
                                <TextBlock Text="🖨️ اختر جهاز الماسح"
                                          Style="{StaticResource LabelTextStyle}"/>
                                <ComboBox x:Name="ScannerComboBox"
                                         Style="{StaticResource GoldenComboBoxStyle}"/>

                                <Button x:Name="RefreshScannersButton"
                                       Content="🔄 تحديث الأجهزة"
                                       Style="{StaticResource GoldenButtonStyle}"
                                       Margin="0,15,0,0"
                                       Click="RefreshScannersButton_Click"/>
                            </StackPanel>
                        </Expander>
                    </Grid>
                </Border>


            </Grid>
            </Grid>

        <!-- أزرار الحفظ والإلغاء -->
        <Border Grid.Row="2" Style="{StaticResource GoldenCardStyle}" Margin="30,10,30,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="💾 حفظ الوثيقة"
                       Style="{StaticResource GoldenButtonStyle}"
                       Width="180" Height="50"
                       FontSize="18"
                       Click="SaveButton_Click"/>

                <Button x:Name="CancelButton"
                       Content="❌ إلغاء"
                       Style="{StaticResource GoldenButtonStyle}"
                       Width="150" Height="50"
                       FontSize="18"
                       Background="{StaticResource SecondaryBackgroundBrush}"
                       Foreground="{StaticResource AccentTextBrush}"
                       BorderBrush="{StaticResource AccentTextBrush}"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
