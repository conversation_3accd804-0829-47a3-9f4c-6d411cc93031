<Window x:Class="Archif.Views.AddDocumentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة وثيقة جديدة"
        Height="750" Width="1300"
        MinHeight="650" MinWidth="1100"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="#F5F5F5">

    <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Padding="30,20"
               Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="28" Height="28" Fill="#4A90E2" VerticalAlignment="Center"/>
                <TextBlock Text="إضافة وثيقة جديدة"
                          FontSize="24" FontWeight="Bold"
                          Margin="15,0,0,0" VerticalAlignment="Center"
                          Foreground="#333333"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الجانب الأيمن - حقول البيانات -->
            <Border Grid.Column="0" Margin="0,0,15,0" Padding="25"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="بيانات الوثيقة"
                                  FontSize="18" FontWeight="Bold"
                                  Margin="0,0,0,20" Foreground="#333333"/>

                        <!-- نوع الكتاب -->
                        <TextBlock Text="نوع الكتاب" FontWeight="Bold" Margin="0,0,0,10" Foreground="#666666"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <RadioButton x:Name="OutgoingRadio"
                                       Content="صادر"
                                       IsChecked="True"
                                       Margin="0,0,20,0"
                                       Checked="DocumentType_Changed"/>
                            <RadioButton x:Name="IncomingRadio"
                                       Content="وارد"
                                       Checked="DocumentType_Changed"/>
                        </StackPanel>

                        <!-- التسلسل التلقائي -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="التسلسل التلقائي" Margin="0,0,0,5" Foreground="#666666"/>
                            <TextBox x:Name="SequenceTextBox"
                                    Height="35" Padding="10,8"
                                    IsReadOnly="True"
                                    Background="#F8F9FA"
                                    BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>

                        <!-- تاريخ الإدخال -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="تاريخ الإدخال" Margin="0,0,0,5" Foreground="#666666"/>
                            <DatePicker x:Name="CreatedDatePicker"
                                       Height="35" Padding="10,8"
                                       IsEnabled="False"
                                       Background="#F8F9FA"
                                       BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>

                        <!-- رقم الكتاب -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="رقم الكتاب *" Margin="0,0,0,5" Foreground="#666666"/>
                            <TextBox x:Name="DocumentNumberTextBox"
                                    Height="35" Padding="10,8"
                                    BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>

                        <!-- تاريخ الكتاب -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="تاريخ الكتاب *" Margin="0,0,0,5" Foreground="#666666"/>
                            <DatePicker x:Name="DocumentDatePicker"
                                       Height="35" Padding="10,8"
                                       BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>

                        <!-- موضوع الكتاب -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="موضوع الكتاب *" Margin="0,0,0,5" Foreground="#666666"/>
                            <TextBox x:Name="SubjectTextBox"
                                    Height="80" Padding="10,8"
                                    TextWrapping="Wrap" AcceptsReturn="True"
                                    VerticalScrollBarVisibility="Auto"
                                    BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>



                        <!-- القسم -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="القسم *" Margin="0,0,0,5" Foreground="#666666"/>
                            <ComboBox x:Name="DepartmentComboBox"
                                     Height="35" Padding="10,8"
                                     BorderBrush="#E0E0E0" BorderThickness="1"
                                     SelectionChanged="DepartmentComboBox_SelectionChanged"/>
                        </StackPanel>

                        <!-- ضبارة الكتاب -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="ضبارة الكتاب *" Margin="0,0,0,5" Foreground="#666666"/>
                            <ComboBox x:Name="FolderComboBox"
                                     Height="35" Padding="10,8"
                                     BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>

                        <!-- جهة الكتاب -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="جهة الكتاب (اختياري)" Margin="0,0,0,5" Foreground="#666666"/>
                            <ComboBox x:Name="OrganizationComboBox"
                                     Height="35" Padding="10,8"
                                     BorderBrush="#E0E0E0" BorderThickness="1"/>
                        </StackPanel>

                        <!-- تسلسل الحفظ -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="تسلسل الحفظ *" Margin="0,0,0,5" Foreground="#666666"/>
                            <TextBox x:Name="ArchiveSequenceTextBox"
                                    Height="35" Padding="10,8"
                                    BorderBrush="#E0E0E0" BorderThickness="1"
                                    Foreground="Red"
                                    TextChanged="ArchiveSequenceTextBox_TextChanged"/>
                            <TextBlock Text="أدخل رقم التسلسل فقط (مثال: 1، 2، 3...)"
                                      FontSize="11" Foreground="#999999"
                                      Margin="0,3,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- الجانب الأيسر - إدارة المرفقات والروابط -->
            <Grid Grid.Column="1" Margin="15,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- قسم المرفقات -->
                <Border Grid.Row="0" Margin="0,0,0,10" Padding="20"
                       Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان المرفقات -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="📎" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="المرفقات والملفات الممسوحة ضوئياً"
                                      FontSize="16" FontWeight="Bold"
                                      Margin="5,0,0,0" VerticalAlignment="Center"
                                      Foreground="#333333"/>
                        </StackPanel>

                        <!-- أزرار إدارة الملفات -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal"
                                   HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Button x:Name="AddFilesButton"
                                   Background="#4A90E2" Foreground="White"
                                   BorderThickness="0" Padding="15,8"
                                   Margin="5" Click="AddFilesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📁" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="إضافة ملفات" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ScanButton"
                                   Background="White" Foreground="#4A90E2"
                                   BorderBrush="#4A90E2" BorderThickness="1"
                                   Padding="15,8" Margin="5"
                                   Click="ScanButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📷" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="مسح ضوئي" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="AddFromCameraButton"
                                   Background="White" Foreground="#28A745"
                                   BorderBrush="#28A745" BorderThickness="1"
                                   Padding="15,8" Margin="5"
                                   Click="AddFromCameraButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📸" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="من الكاميرا" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- قائمة الملفات المرفقة -->
                        <ListBox Grid.Row="2" x:Name="AttachmentsListBox"
                                Background="Transparent" BorderThickness="0">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,2" Padding="10"
                                           Background="#F8F9FA" BorderBrush="#E0E0E0"
                                           BorderThickness="1" CornerRadius="4">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="{Binding FileIcon}" FontSize="20"
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>

                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding FileName}" FontWeight="Bold"
                                                          Foreground="#333333"/>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding TypeDisplay}" FontSize="12"
                                                              Foreground="#666666" Margin="0,0,10,0"/>
                                                    <TextBlock Text="{Binding FileSizeFormatted}" FontSize="12"
                                                              Foreground="#666666"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <Button Grid.Column="2" Content="👁️" ToolTip="معاينة"
                                                   Background="Transparent" Foreground="#4A90E2"
                                                   BorderThickness="0" Width="30" Height="30"
                                                   Click="PreviewAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="3" Content="📂" ToolTip="فتح"
                                                   Background="Transparent" Foreground="#28A745"
                                                   BorderThickness="0" Width="30" Height="30"
                                                   Click="OpenAttachment_Click" Tag="{Binding}"/>

                                            <Button Grid.Column="4" Content="🗑️" ToolTip="حذف"
                                                   Background="Transparent" Foreground="#DC3545"
                                                   BorderThickness="0" Width="30" Height="30"
                                                   Click="RemoveAttachment_Click" Tag="{Binding}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <!-- إعدادات الماسح الضوئي -->
                        <Expander Grid.Row="3" Header="إعدادات الماسح الضوئي"
                                 Margin="0,10,0,0" Background="Transparent">
                            <StackPanel Margin="0,10,0,0">
                                <StackPanel Margin="0,0,0,10">
                                    <TextBlock Text="اختر جهاز الماسح"
                                              Margin="0,0,0,5" Foreground="#666666"/>
                                    <ComboBox x:Name="ScannerComboBox"
                                             Height="35" Padding="10,8"
                                             BorderBrush="#E0E0E0" BorderThickness="1"/>
                                </StackPanel>

                                <Button x:Name="RefreshScannersButton"
                                       Content="تحديث الأجهزة"
                                       Background="White" Foreground="#666666"
                                       BorderBrush="#E0E0E0" BorderThickness="1"
                                       Padding="10,8" Click="RefreshScannersButton_Click"/>
                            </StackPanel>
                        </Expander>
                    </Grid>
                </Border>


            </Grid>
            </Grid>

        <!-- أزرار الحفظ والإلغاء -->
        <Border Grid.Row="2"
               Background="White" BorderThickness="0,1,0,0"
               BorderBrush="#E0E0E0" Padding="30,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ الوثيقة"
                       Background="#4A90E2" Foreground="White"
                       BorderThickness="0" Width="140" Height="40"
                       Margin="10,0" Click="SaveButton_Click"/>

                <Button x:Name="CancelButton"
                       Content="إلغاء"
                       Background="White" Foreground="#666666"
                       BorderBrush="#E0E0E0" BorderThickness="1"
                       Width="120" Height="40"
                       Margin="10,0" Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
