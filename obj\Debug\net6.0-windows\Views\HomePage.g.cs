﻿#pragma checksum "..\..\..\..\Views\HomePage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C214B60E77ADA87E77E4F92CC149A21E3E7734B2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Archif.Views {
    
    
    /// <summary>
    /// HomePage
    /// </summary>
    public partial class HomePage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 37 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDocumentsText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutgoingCountText;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IncomingCountText;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ThisMonthCountText;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDocumentButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportExcelButton;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPdfButton;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuickSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AdvancedSearchButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AdvancedFiltersPanel;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YearFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DepartmentFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FolderFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OrganizationFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DocumentNumberFilterTextBox;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentsCountText;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DocumentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 542 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PageSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 550 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRecordsText;
        
        #line default
        #line hidden
        
        
        #line 556 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FirstPageButton;
        
        #line default
        #line hidden
        
        
        #line 561 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousPageButton;
        
        #line default
        #line hidden
        
        
        #line 566 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageInfoText;
        
        #line default
        #line hidden
        
        
        #line 569 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextPageButton;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\Views\HomePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LastPageButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Archif;V2.0.0.0;component/views/homepage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\HomePage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Views\HomePage.xaml"
            ((Archif.Views.HomePage)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.UserControl_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TotalDocumentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.OutgoingCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.IncomingCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ThisMonthCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.AddDocumentButton = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\..\Views\HomePage.xaml"
            this.AddDocumentButton.Click += new System.Windows.RoutedEventHandler(this.AddDocumentButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ExportExcelButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\..\Views\HomePage.xaml"
            this.ExportExcelButton.Click += new System.Windows.RoutedEventHandler(this.ExportExcelButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Views\HomePage.xaml"
            this.ExportPdfButton.Click += new System.Windows.RoutedEventHandler(this.ExportPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.QuickSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 179 "..\..\..\..\Views\HomePage.xaml"
            this.QuickSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.QuickSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AdvancedSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\Views\HomePage.xaml"
            this.AdvancedSearchButton.Click += new System.Windows.RoutedEventHandler(this.AdvancedSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\HomePage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AdvancedFiltersPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.YearFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 260 "..\..\..\..\Views\HomePage.xaml"
            this.YearFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ComboBoxFilterChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.DepartmentFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 266 "..\..\..\..\Views\HomePage.xaml"
            this.DepartmentFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ComboBoxFilterChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.FolderFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 272 "..\..\..\..\Views\HomePage.xaml"
            this.FolderFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ComboBoxFilterChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.OrganizationFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 278 "..\..\..\..\Views\HomePage.xaml"
            this.OrganizationFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ComboBoxFilterChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 294 "..\..\..\..\Views\HomePage.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ComboBoxFilterChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 304 "..\..\..\..\Views\HomePage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilterChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 310 "..\..\..\..\Views\HomePage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilterChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.DocumentNumberFilterTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 317 "..\..\..\..\Views\HomePage.xaml"
            this.DocumentNumberFilterTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TextFilterChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ApplyFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 331 "..\..\..\..\Views\HomePage.xaml"
            this.ApplyFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ApplyFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\Views\HomePage.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.DocumentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.DocumentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 378 "..\..\..\..\Views\HomePage.xaml"
            this.DocumentsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.DocumentsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 29:
            this.PageSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 543 "..\..\..\..\Views\HomePage.xaml"
            this.PageSizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PageSizeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            this.TotalRecordsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.FirstPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 560 "..\..\..\..\Views\HomePage.xaml"
            this.FirstPageButton.Click += new System.Windows.RoutedEventHandler(this.FirstPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.PreviousPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 565 "..\..\..\..\Views\HomePage.xaml"
            this.PreviousPageButton.Click += new System.Windows.RoutedEventHandler(this.PreviousPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.PageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.NextPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 573 "..\..\..\..\Views\HomePage.xaml"
            this.NextPageButton.Click += new System.Windows.RoutedEventHandler(this.NextPageButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.LastPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 578 "..\..\..\..\Views\HomePage.xaml"
            this.LastPageButton.Click += new System.Windows.RoutedEventHandler(this.LastPageButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 25:
            
            #line 484 "..\..\..\..\Views\HomePage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDocumentButton_Click);
            
            #line default
            #line hidden
            break;
            case 26:
            
            #line 494 "..\..\..\..\Views\HomePage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditDocumentButton_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 504 "..\..\..\..\Views\HomePage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteDocumentButton_Click);
            
            #line default
            #line hidden
            break;
            case 28:
            
            #line 514 "..\..\..\..\Views\HomePage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintDocumentButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

