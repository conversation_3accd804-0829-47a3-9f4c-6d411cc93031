using System;
using System.IO;
using System.Windows;
using System.Text.Json;
using System.Diagnostics;
using Archif.Constants;

namespace Archif.Helpers
{
    /// <summary>
    /// معالج أخطاء متقدم مع نظام Logging شامل
    /// </summary>
    public static class AdvancedErrorHandler
    {
        private static readonly string LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "Archif", "Logs");

        private static readonly object LogLock = new object();

        static AdvancedErrorHandler()
        {
            if (!Directory.Exists(LogDirectory))
                Directory.CreateDirectory(LogDirectory);
        }

        public enum LogLevel
        {
            Info,
            Warning,
            Error,
            Critical,
            Debug
        }

        public class LogEntry
        {
            public DateTime Timestamp { get; set; } = DateTime.Now;
            public LogLevel Level { get; set; }
            public string Message { get; set; } = string.Empty;
            public string Context { get; set; } = string.Empty;
            public string StackTrace { get; set; } = string.Empty;
            public string UserAction { get; set; } = string.Empty;
            public string SystemInfo { get; set; } = string.Empty;
        }

        /// <summary>
        /// تسجيل خطأ مع معلومات شاملة
        /// </summary>
        public static void LogError(Exception ex, string context = "", string userAction = "")
        {
            var logEntry = new LogEntry
            {
                Level = LogLevel.Error,
                Message = ex.Message,
                Context = context,
                StackTrace = ex.StackTrace ?? "",
                UserAction = userAction,
                SystemInfo = GetSystemInfo()
            };

            WriteLog(logEntry);
            ShowUserFriendlyError(ex, context);
        }

        /// <summary>
        /// تسجيل خطأ حرج مع إجراءات الطوارئ
        /// </summary>
        public static void LogCriticalError(Exception ex, string context = "", bool createEmergencyBackup = true)
        {
            var logEntry = new LogEntry
            {
                Level = LogLevel.Critical,
                Message = ex.Message,
                Context = context,
                StackTrace = ex.StackTrace ?? "",
                SystemInfo = GetSystemInfo()
            };

            WriteLog(logEntry);

            // إنشاء نسخة احتياطية طارئة
            if (createEmergencyBackup)
            {
                try
                {
                    CreateEmergencyBackup();
                }
                catch (Exception backupEx)
                {
                    LogError(backupEx, "إنشاء نسخة احتياطية طارئة");
                }
            }

            ShowCriticalErrorDialog(ex, context);
        }

        /// <summary>
        /// تسجيل معلومات عامة
        /// </summary>
        public static void LogInfo(string message, string context = "")
        {
            var logEntry = new LogEntry
            {
                Level = LogLevel.Info,
                Message = message,
                Context = context
            };

            WriteLog(logEntry);
        }

        /// <summary>
        /// تسجيل تحذير
        /// </summary>
        public static void LogWarning(string message, string context = "")
        {
            var logEntry = new LogEntry
            {
                Level = LogLevel.Warning,
                Message = message,
                Context = context
            };

            WriteLog(logEntry);
        }

        /// <summary>
        /// كتابة السجل إلى الملف
        /// </summary>
        private static void WriteLog(LogEntry entry)
        {
            lock (LogLock)
            {
                try
                {
                    var logFileName = $"archif_log_{DateTime.Now:yyyyMMdd}.json";
                    var logFilePath = Path.Combine(LogDirectory, logFileName);

                    var logText = JsonSerializer.Serialize(entry, new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });

                    File.AppendAllText(logFilePath, logText + Environment.NewLine);

                    // تنظيف السجلات القديمة (أكثر من 30 يوم)
                    CleanOldLogs();
                }
                catch
                {
                    // في حالة فشل الكتابة، لا نريد أن نسبب خطأ إضافي
                }
            }
        }

        /// <summary>
        /// تنظيف السجلات القديمة
        /// </summary>
        private static void CleanOldLogs()
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-30);
                var logFiles = Directory.GetFiles(LogDirectory, "archif_log_*.json");

                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }

        /// <summary>
        /// الحصول على معلومات النظام
        /// </summary>
        private static string GetSystemInfo()
        {
            try
            {
                return $"OS: {Environment.OSVersion}, " +
                       $"CLR: {Environment.Version}, " +
                       $"Memory: {GC.GetTotalMemory(false) / 1024 / 1024} MB, " +
                       $"Processors: {Environment.ProcessorCount}";
            }
            catch
            {
                return "معلومات النظام غير متاحة";
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية طارئة
        /// </summary>
        private static void CreateEmergencyBackup()
        {
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "Archif", "archif.db");

            if (File.Exists(dbPath))
            {
                var emergencyBackupPath = Path.Combine(LogDirectory, 
                    $"emergency_backup_{DateTime.Now:yyyyMMdd_HHmmss}.db");
                File.Copy(dbPath, emergencyBackupPath, true);
            }
        }

        /// <summary>
        /// عرض رسالة خطأ مفهومة للمستخدم
        /// </summary>
        private static void ShowUserFriendlyError(Exception ex, string context)
        {
            var userMessage = GetUserFriendlyMessage(ex, context);
            MessageBox.Show(userMessage, "خطأ في النظام", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة خطأ حرج
        /// </summary>
        private static void ShowCriticalErrorDialog(Exception ex, string context)
        {
            var message = $"حدث خطأ حرج في النظام.\n\n" +
                         $"السياق: {context}\n" +
                         $"الرسالة: {ex.Message}\n\n" +
                         $"تم إنشاء نسخة احتياطية طارئة.\n" +
                         $"يرجى إعادة تشغيل التطبيق والتواصل مع الدعم الفني.";

            MessageBox.Show(message, "خطأ حرج", 
                MessageBoxButton.OK, MessageBoxImage.Stop);
        }

        /// <summary>
        /// تحويل رسالة الخطأ التقنية إلى رسالة مفهومة
        /// </summary>
        private static string GetUserFriendlyMessage(Exception ex, string context)
        {
            return ex switch
            {
                UnauthorizedAccessException => "ليس لديك صلاحية للوصول إلى هذا المورد.",
                FileNotFoundException => "الملف المطلوب غير موجود.",
                DirectoryNotFoundException => "المجلد المطلوب غير موجود.",
                IOException => "خطأ في عملية القراءة أو الكتابة.",
                OutOfMemoryException => "الذاكرة المتاحة غير كافية.",
                TimeoutException => "انتهت مهلة العملية.",
                InvalidOperationException => "العملية المطلوبة غير صالحة في الوقت الحالي.",
                ArgumentException => "البيانات المدخلة غير صحيحة.",
                _ => $"حدث خطأ غير متوقع في {context}. يرجى المحاولة مرة أخرى."
            };
        }

        /// <summary>
        /// تصدير السجلات لفترة معينة
        /// </summary>
        public static void ExportLogs(DateTime fromDate, DateTime toDate, string exportPath)
        {
            try
            {
                var logFiles = Directory.GetFiles(LogDirectory, "archif_log_*.json");
                var exportData = new List<LogEntry>();

                foreach (var file in logFiles)
                {
                    var fileDate = ExtractDateFromLogFileName(file);
                    if (fileDate >= fromDate.Date && fileDate <= toDate.Date)
                    {
                        var content = File.ReadAllText(file);
                        var lines = content.Split(new[] { Environment.NewLine }, 
                            StringSplitOptions.RemoveEmptyEntries);

                        foreach (var line in lines)
                        {
                            try
                            {
                                var entry = JsonSerializer.Deserialize<LogEntry>(line);
                                if (entry != null && entry.Timestamp >= fromDate && entry.Timestamp <= toDate)
                                {
                                    exportData.Add(entry);
                                }
                            }
                            catch
                            {
                                // تجاهل الأسطر التالفة
                            }
                        }
                    }
                }

                var exportJson = JsonSerializer.Serialize(exportData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                File.WriteAllText(exportPath, exportJson);
            }
            catch (Exception ex)
            {
                LogError(ex, "تصدير السجلات");
            }
        }

        private static DateTime ExtractDateFromLogFileName(string filePath)
        {
            try
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var datePart = fileName.Replace("archif_log_", "");
                return DateTime.ParseExact(datePart, "yyyyMMdd", null);
            }
            catch
            {
                return DateTime.MinValue;
            }
        }
    }
}
