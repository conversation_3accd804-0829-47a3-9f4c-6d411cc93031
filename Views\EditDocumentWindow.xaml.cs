using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Archif.Models;
using Archif.Services;
using MessageBox = System.Windows.MessageBox;
using Button = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// نافذة تعديل الوثيقة
    /// </summary>
    public partial class EditDocumentWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly FileService _fileService;
        private readonly Document _document;
        private readonly ObservableCollection<AttachmentInfo> _attachments;
        private readonly ObservableCollection<AttachmentInfo> _newAttachments;
        private readonly List<int> _deletedAttachmentIds;

        public event EventHandler? DocumentUpdated;

        public EditDocumentWindow(DatabaseService databaseService, Document document)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _fileService = new FileService();
            _document = document;
            _attachments = new ObservableCollection<AttachmentInfo>();
            _newAttachments = new ObservableCollection<AttachmentInfo>();
            _deletedAttachmentIds = new List<int>();

            AttachmentsListBox.ItemsSource = _attachments;

            InitializeForm();
        }

        private async void InitializeForm()
        {
            try
            {
                WindowTitleText.Text = $"تعديل الوثيقة - {_document.DocumentNumber}";

                // تحميل البيانات الأساسية
                await LoadComboBoxData();

                // تحميل بيانات الوثيقة
                LoadDocumentData();

                // تحميل المرفقات الموجودة
                await LoadExistingAttachments();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadComboBoxData()
        {
            try
            {
                // تحميل الأقسام
                var departments = await _databaseService.GetDepartmentsAsync();
                DepartmentComboBox.ItemsSource = departments;

                // تحميل الجهات
                var organizations = await _databaseService.GetOrganizationsAsync();
                var organizationsList = organizations.ToList();
                organizationsList.Insert(0, new Organization { Id = 0, Name = "-- اختر الجهة --" });
                OrganizationComboBox.ItemsSource = organizationsList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDocumentData()
        {
            try
            {
                DocumentNumberTextBox.Text = _document.DocumentNumber;
                DocumentDatePicker.SelectedDate = _document.DocumentDate;
                SubjectTextBox.Text = _document.Subject;
                ArchiveSequenceTextBox.Text = _document.ArchiveSequence ?? "";

                // تحديد نوع الوثيقة
                if (_document.Type == DocumentType.Outgoing)
                    OutgoingRadio.IsChecked = true;
                else
                    IncomingRadio.IsChecked = true;

                // تحديد القسم
                DepartmentComboBox.SelectedValue = _document.DepartmentId;

                // تحديد الضبارة
                if (_document.FolderId > 0)
                    FolderComboBox.SelectedValue = _document.FolderId;

                // تحديد الجهة
                if (_document.OrganizationId.HasValue)
                    OrganizationComboBox.SelectedValue = _document.OrganizationId.Value;
                else
                    OrganizationComboBox.SelectedIndex = 0;

                // عرض التسلسل التلقائي
                SequenceTextBox.Text = _document.Id.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadExistingAttachments()
        {
            try
            {
                var existingAttachments = await _databaseService.GetAttachmentsByDocumentIdAsync(_document.Id);

                foreach (var attachment in existingAttachments)
                {
                    var attachmentInfo = new AttachmentInfo
                    {
                        Id = attachment.Id,
                        FileName = attachment.FileName,
                        FilePath = attachment.FilePath,
                        FileType = attachment.FileType,
                        MimeType = attachment.MimeType,
                        FileSize = attachment.FileSize,
                        Type = attachment.Type,
                        Description = attachment.Description,
                        CreatedDate = attachment.CreatedDate,
                        IsExisting = true
                    };

                    _attachments.Add(attachmentInfo);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المرفقات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DepartmentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (DepartmentComboBox.SelectedItem is Department selectedDepartment)
                {
                    var folders = await _databaseService.GetFoldersByDepartmentIdAsync(selectedDepartment.Id);
                    FolderComboBox.ItemsSource = folders;

                    // تحديد الضبارة الحالية إذا كانت تنتمي لنفس القسم
                    if (_document.FolderId > 0)
                    {
                        var currentFolder = folders.FirstOrDefault(f => f.Id == _document.FolderId);
                        if (currentFolder != null)
                            FolderComboBox.SelectedValue = _document.FolderId;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الضبائر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DocumentType_Changed(object sender, RoutedEventArgs e)
        {
            ValidateArchiveSequenceFormat();
        }

        private void ArchiveSequenceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateArchiveSequenceFormat();
        }

        private void ValidateArchiveSequenceFormat()
        {
            try
            {
                var text = ArchiveSequenceTextBox.Text;
                if (!string.IsNullOrWhiteSpace(text))
                {
                    if (int.TryParse(text, out int number) && number > 0)
                    {
                        ArchiveSequenceTextBox.BorderBrush = new SolidColorBrush(Colors.Green);
                        ArchiveSequenceTextBox.ToolTip = "تنسيق صحيح";
                    }
                    else
                    {
                        ArchiveSequenceTextBox.BorderBrush = new SolidColorBrush(Colors.Red);
                        ArchiveSequenceTextBox.ToolTip = "يجب إدخال رقم صحيح أكبر من صفر";
                    }
                }
                else
                {
                    ArchiveSequenceTextBox.BorderBrush = new SolidColorBrush(Colors.LightGray);
                    ArchiveSequenceTextBox.ToolTip = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من تنسيق التسلسل: {ex.Message}");
            }
        }

        private async void AddFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog()
                {
                    Title = "اختر الملفات المراد إرفاقها",
                    Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pptx;*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.txt;*.zip;*.rar|" +
                            "ملفات PDF|*.pdf|" +
                            "مستندات Word|*.doc;*.docx|" +
                            "جداول Excel|*.xls;*.xlsx|" +
                            "عروض PowerPoint|*.ppt;*.pptx|" +
                            "الصور|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff|" +
                            "ملفات نصية|*.txt|" +
                            "ملفات مضغوطة|*.zip;*.rar|" +
                            "جميع الملفات|*.*",
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    AddFilesButton.IsEnabled = false;
                    AddFilesButton.Content = "جاري الإضافة...";

                    var addedCount = 0;
                    foreach (var fileName in openFileDialog.FileNames)
                    {
                        try
                        {
                            if (!_fileService.IsValidFileType(fileName))
                            {
                                MessageBox.Show($"نوع الملف غير مدعوم: {Path.GetFileName(fileName)}", "تحذير",
                                              MessageBoxButton.OK, MessageBoxImage.Warning);
                                continue;
                            }

                            await AddAttachmentAsync(fileName);
                            addedCount++;
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في إضافة الملف {Path.GetFileName(fileName)}: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }

                    if (addedCount > 0)
                    {
                        MessageBox.Show($"تم إضافة {addedCount} ملف بنجاح", "نجح",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الملفات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                AddFilesButton.IsEnabled = true;
                var originalContent = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                originalContent.Children.Add(new TextBlock { Text = "📁", FontSize = 16, VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(0, 0, 5, 0) });
                originalContent.Children.Add(new TextBlock { Text = "إضافة ملفات", Margin = new Thickness(5, 0, 0, 0) });
                AddFilesButton.Content = originalContent;
            }
        }

        private Task AddAttachmentAsync(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var attachment = new AttachmentInfo
            {
                FileName = fileInfo.Name,
                FilePath = filePath,
                FileType = fileInfo.Extension.ToLower(),
                MimeType = GetMimeType(fileInfo.Extension),
                FileSize = fileInfo.Length,
                Type = DetermineAttachmentType(fileInfo.Extension),
                CreatedDate = DateTime.Now,
                IsExisting = false
            };

            _attachments.Add(attachment);
            _newAttachments.Add(attachment);
            return Task.CompletedTask;
        }

        private string GetMimeType(string extension)
        {
            return extension.ToLower() switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                ".txt" => "text/plain",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                _ => "application/octet-stream"
            };
        }

        private AttachmentType DetermineAttachmentType(string extension)
        {
            return extension.ToLower() switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" or ".tif" => AttachmentType.ScannedImage,
                ".pdf" => AttachmentType.ScannedPdf,
                _ => AttachmentType.Document
            };
        }

        private void ScanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "WiaAcmgr.exe",
                    Arguments = "/ScannerDeviceDialog",
                    UseShellExecute = true
                });

                MessageBox.Show("تم فتح تطبيق المسح الضوئي. بعد المسح، يمكنك إضافة الملفات باستخدام زر 'إضافة ملفات'.",
                              "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"لم يتم العثور على تطبيق المسح الضوئي أو فشل في تشغيله.\n\nتفاصيل الخطأ: {ex.Message}",
                              "خطأ في المسح الضوئي", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddFromCameraButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var cameraProcess = new ProcessStartInfo
                {
                    FileName = "microsoft.windows.camera:",
                    UseShellExecute = true
                };

                Process.Start(cameraProcess);

                MessageBox.Show("تم فتح تطبيق الكاميرا. بعد التقاط الصور، يمكنك إضافتها باستخدام زر 'إضافة ملفات'.",
                              "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"لم يتم العثور على تطبيق الكاميرا أو فشل في تشغيله.\n\nتفاصيل الخطأ: {ex.Message}",
                              "خطأ في الكاميرا", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void PreviewAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is AttachmentInfo attachment)
            {
                try
                {
                    await _fileService.OpenAttachmentAsync(attachment.FilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في معاينة الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void OpenAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is AttachmentInfo attachment)
            {
                try
                {
                    await _fileService.OpenAttachmentAsync(attachment.FilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void RemoveAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is AttachmentInfo attachment)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الملف '{attachment.FileName}'؟",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _attachments.Remove(attachment);

                    if (attachment.IsExisting && attachment.Id > 0)
                    {
                        _deletedAttachmentIds.Add(attachment.Id);
                    }
                    else
                    {
                        _newAttachments.Remove(attachment);
                    }
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                SaveButton.IsEnabled = false;
                SaveButton.Content = "جاري الحفظ...";

                await SaveDocument();

                MessageBox.Show("تم حفظ التعديلات بنجاح", "نجح",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DocumentUpdated?.Invoke(this, EventArgs.Empty);
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
                var originalContent = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                originalContent.Children.Add(new TextBlock { Text = "💾", FontSize = 16, VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(0, 0, 8, 0) });
                originalContent.Children.Add(new TextBlock { Text = "حفظ التعديلات", Margin = new Thickness(5, 0, 0, 0) });
                SaveButton.Content = originalContent;
            }
        }

        private bool ValidateForm()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(DocumentNumberTextBox.Text))
            {
                MessageBox.Show("رقم الوثيقة مطلوب", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DocumentNumberTextBox.Focus();
                return false;
            }

            if (!DocumentDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("تاريخ الوثيقة مطلوب", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DocumentDatePicker.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(SubjectTextBox.Text))
            {
                MessageBox.Show("موضوع الوثيقة مطلوب", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                SubjectTextBox.Focus();
                return false;
            }

            if (DepartmentComboBox.SelectedItem == null)
            {
                MessageBox.Show("يجب اختيار القسم", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DepartmentComboBox.Focus();
                return false;
            }

            if (FolderComboBox.SelectedItem == null)
            {
                MessageBox.Show("يجب اختيار الضبارة", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                FolderComboBox.Focus();
                return false;
            }

            // التحقق من تسلسل الحفظ
            if (!int.TryParse(ArchiveSequenceTextBox.Text, out int archiveSequence) || archiveSequence <= 0)
            {
                MessageBox.Show("تسلسل الحفظ يجب أن يكون رقم صحيح أكبر من صفر",
                              "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ArchiveSequenceTextBox.Focus();
                return false;
            }

            return true;
        }

        private async Task SaveDocument()
        {
            var department = (Department)DepartmentComboBox.SelectedItem;
            var folder = (Folder)FolderComboBox.SelectedItem;
            var organization = OrganizationComboBox.SelectedItem as Organization;

            // تحديث بيانات الوثيقة
            _document.DocumentNumber = DocumentNumberTextBox.Text.Trim();
            _document.DocumentDate = DocumentDatePicker.SelectedDate!.Value;
            _document.Subject = SubjectTextBox.Text.Trim();
            _document.Type = OutgoingRadio.IsChecked == true ? DocumentType.Outgoing : DocumentType.Incoming;
            _document.DepartmentId = department.Id;
            _document.FolderId = folder.Id;
            _document.OrganizationId = organization?.Id == 0 ? null : organization?.Id;
            _document.ArchiveSequence = ArchiveSequenceTextBox.Text.Trim();

            // حفظ الوثيقة
            await _databaseService.UpdateDocumentAsync(_document);

            // حذف المرفقات المحذوفة
            foreach (var deletedId in _deletedAttachmentIds)
            {
                await _databaseService.DeleteAttachmentFileAsync(deletedId);
            }

            // حفظ المرفقات الجديدة
            await SaveNewAttachments();
        }

        private async Task SaveNewAttachments()
        {
            try
            {
                foreach (var attachmentInfo in _newAttachments)
                {
                    // نسخ الملف إلى مجلد المرفقات
                    var savedAttachment = await _fileService.SaveAttachmentAsync(
                        attachmentInfo.FilePath, _document.Id, attachmentInfo.Type, attachmentInfo.Description);

                    // حفظ معلومات المرفق في قاعدة البيانات
                    var attachment = new Attachment
                    {
                        DocumentId = _document.Id,
                        FileName = savedAttachment.FileName,
                        FilePath = savedAttachment.FilePath,
                        FileType = savedAttachment.FileType,
                        MimeType = savedAttachment.MimeType,
                        FileSize = savedAttachment.FileSize,
                        Type = savedAttachment.Type,
                        Description = savedAttachment.Description,
                        CreatedDate = savedAttachment.CreatedDate
                    };

                    await _databaseService.AddAttachmentAsync(attachment);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المرفقات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إلغاء التعديلات؟ ستفقد جميع التغييرات غير المحفوظة.",
                                       "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }
    }
}
