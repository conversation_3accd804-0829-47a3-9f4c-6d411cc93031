using Microsoft.EntityFrameworkCore;
using Archif.Data;
using Archif.Models;
using System.IO;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace Archif.Services
{
    /// <summary>
    /// خدمة قاعدة البيانات المحسنة للاستقرار طويل المدى
    /// </summary>
    public class ImprovedDatabaseService : IDisposable
    {
        private readonly ILogger<ImprovedDatabaseService> _logger;
        private readonly ConcurrentDictionary<string, object> _cache;
        private readonly SemaphoreSlim _connectionSemaphore;
        private ArchifDbContext? _context;
        private bool _disposed = false;

        public ImprovedDatabaseService(ILogger<ImprovedDatabaseService> logger = null)
        {
            _logger = logger ?? CreateDefaultLogger();
            _cache = new ConcurrentDictionary<string, object>();
            _connectionSemaphore = new SemaphoreSlim(10, 10); // حد أقصى 10 اتصالات متزامنة
            InitializeDatabase();
        }

        private ILogger<ImprovedDatabaseService> CreateDefaultLogger()
        {
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().AddDebug());
            return loggerFactory.CreateLogger<ImprovedDatabaseService>();
        }

        private ArchifDbContext GetContext()
        {
            if (_context == null || _disposed)
            {
                _context = new ArchifDbContext();
            }
            return _context;
        }

        public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
        {
            var retryCount = 0;
            while (retryCount < maxRetries)
            {
                try
                {
                    await _connectionSemaphore.WaitAsync();
                    return await operation();
                }
                catch (Exception ex) when (retryCount < maxRetries - 1)
                {
                    retryCount++;
                    _logger?.LogWarning($"محاولة {retryCount} فشلت: {ex.Message}");
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount))); // Exponential backoff
                }
                finally
                {
                    _connectionSemaphore.Release();
                }
            }
            throw new InvalidOperationException($"فشل في تنفيذ العملية بعد {maxRetries} محاولات");
        }

        // تحسين استعلامات الوثائق مع Pagination محسن
        public async Task<(List<Document> Documents, int TotalCount)> GetDocumentsPagedOptimizedAsync(
            int page, int pageSize, string searchText = null, 
            int? departmentId = null, int? folderId = null, 
            int? organizationId = null, DocumentType? documentType = null,
            DateTime? fromDate = null, DateTime? toDate = null, 
            string documentNumber = null)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                var cacheKey = $"docs_{page}_{pageSize}_{searchText}_{departmentId}_{folderId}_{organizationId}_{documentType}_{fromDate}_{toDate}_{documentNumber}";
                
                if (_cache.TryGetValue(cacheKey, out var cachedResult))
                {
                    var cached = (ValueTuple<List<Document>, int>)cachedResult;
                    if (DateTime.Now.Subtract(DateTime.Now).TotalMinutes < 5) // Cache for 5 minutes
                    {
                        return cached;
                    }
                    _cache.TryRemove(cacheKey, out _);
                }

                var context = GetContext();
                var query = context.Documents
                    .Include(d => d.Department)
                    .Include(d => d.Folder)
                    .Include(d => d.Organization)
                    .Where(d => !d.IsDeleted)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (!string.IsNullOrWhiteSpace(searchText))
                {
                    query = query.Where(d => 
                        d.Subject.Contains(searchText) || 
                        d.DocumentNumber.Contains(searchText) ||
                        d.FromTo.Contains(searchText));
                }

                if (departmentId.HasValue)
                    query = query.Where(d => d.DepartmentId == departmentId.Value);

                if (folderId.HasValue)
                    query = query.Where(d => d.FolderId == folderId.Value);

                if (organizationId.HasValue)
                    query = query.Where(d => d.OrganizationId == organizationId.Value);

                if (documentType.HasValue)
                    query = query.Where(d => d.Type == documentType.Value);

                if (fromDate.HasValue)
                    query = query.Where(d => d.DocumentDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(d => d.DocumentDate <= toDate.Value);

                if (!string.IsNullOrWhiteSpace(documentNumber))
                    query = query.Where(d => d.DocumentNumber.Contains(documentNumber));

                var totalCount = await query.CountAsync();
                var documents = await query
                    .OrderByDescending(d => d.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var result = (documents, totalCount);
                _cache.TryAdd(cacheKey, result);
                
                return result;
            });
        }

        // تحسين إدارة المرفقات
        public async Task<bool> OptimizeAttachmentsStorageAsync()
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                var context = GetContext();
                var attachmentsPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "Archif", "Attachments");

                if (!Directory.Exists(attachmentsPath))
                    return true;

                var orphanedFiles = new List<string>();
                var allFiles = Directory.GetFiles(attachmentsPath, "*", SearchOption.AllDirectories);
                var dbAttachments = await context.Attachments.Select(a => a.FilePath).ToListAsync();

                foreach (var file in allFiles)
                {
                    var relativePath = Path.GetRelativePath(attachmentsPath, file);
                    if (!dbAttachments.Any(a => a.Contains(relativePath)))
                    {
                        orphanedFiles.Add(file);
                    }
                }

                // حذف الملفات المهجورة
                foreach (var file in orphanedFiles)
                {
                    try
                    {
                        File.Delete(file);
                        _logger?.LogInformation($"تم حذف الملف المهجور: {file}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning($"فشل في حذف الملف {file}: {ex.Message}");
                    }
                }

                return true;
            });
        }

        // تحسين النسخ الاحتياطي
        public async Task<bool> CreateOptimizedBackupAsync(string backupPath, bool includeAttachments = true)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                var context = GetContext();
                var dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "Archif", "archif.db");

                if (!File.Exists(dbPath))
                    return false;

                // إنشاء مجلد النسخة الاحتياطية
                var backupDir = Path.GetDirectoryName(backupPath);
                if (!Directory.Exists(backupDir))
                    Directory.CreateDirectory(backupDir);

                // نسخ قاعدة البيانات
                await context.Database.CloseConnectionAsync();
                File.Copy(dbPath, backupPath, true);
                await context.Database.OpenConnectionAsync();

                // نسخ المرفقات إذا طُلب ذلك
                if (includeAttachments)
                {
                    var attachmentsPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                        "Archif", "Attachments");

                    if (Directory.Exists(attachmentsPath))
                    {
                        var backupAttachmentsPath = Path.Combine(backupDir, "Attachments");
                        await CopyDirectoryAsync(attachmentsPath, backupAttachmentsPath);
                    }
                }

                _logger?.LogInformation($"تم إنشاء نسخة احتياطية في: {backupPath}");
                return true;
            });
        }

        private async Task CopyDirectoryAsync(string sourceDir, string destDir)
        {
            await Task.Run(() =>
            {
                if (!Directory.Exists(destDir))
                    Directory.CreateDirectory(destDir);

                foreach (var file in Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories))
                {
                    var relativePath = Path.GetRelativePath(sourceDir, file);
                    var destFile = Path.Combine(destDir, relativePath);
                    var destFileDir = Path.GetDirectoryName(destFile);
                    
                    if (!Directory.Exists(destFileDir))
                        Directory.CreateDirectory(destFileDir);
                    
                    File.Copy(file, destFile, true);
                }
            });
        }

        // تنظيف الذاكرة والموارد
        public async Task CleanupResourcesAsync()
        {
            _cache.Clear();
            
            if (_context != null)
            {
                await _context.Database.CloseConnectionAsync();
                await _context.DisposeAsync();
                _context = null;
            }

            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _context?.Dispose();
                _connectionSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
