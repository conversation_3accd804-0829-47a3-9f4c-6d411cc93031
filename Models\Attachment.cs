using System.ComponentModel.DataAnnotations;

namespace Archif.Models
{
    /// <summary>
    /// نموذج المرفقات والملفات الممسوحة ضوئياً
    /// </summary>
    public class Attachment
    {
        public int Id { get; set; }

        public int DocumentId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [StringLength(50)]
        public string FileType { get; set; } = string.Empty;

        [StringLength(100)]
        public string MimeType { get; set; } = string.Empty;

        public long FileSize { get; set; }

        public AttachmentType Type { get; set; } = AttachmentType.Document;

        [StringLength(1000)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual Document Document { get; set; } = null!;
    }

    /// <summary>
    /// أنواع المرفقات
    /// </summary>
    public enum AttachmentType
    {
        Document = 0,      // وثيقة عادية
        ScannedImage = 1,  // صورة ممسوحة ضوئياً
        ScannedPdf = 2,    // PDF ممسوح ضوئياً
        OriginalFile = 3   // ملف أصلي
    }

    /// <summary>
    /// معلومات المرفق للعرض في الواجهة
    /// </summary>
    public class AttachmentInfo
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public AttachmentType Type { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsExisting { get; set; } = false;
        public string FileSizeFormatted => FormatFileSize(FileSize);
        public string TypeDisplay => GetTypeDisplay(Type);
        public string FileIcon => GetFileIcon(FileType);

        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }

        private static string GetTypeDisplay(AttachmentType type)
        {
            return type switch
            {
                AttachmentType.Document => "وثيقة",
                AttachmentType.ScannedImage => "صورة ممسوحة",
                AttachmentType.ScannedPdf => "PDF ممسوح",
                AttachmentType.OriginalFile => "ملف أصلي",
                _ => "غير محدد"
            };
        }

        private static string GetFileIcon(string fileType)
        {
            return fileType.ToLower() switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" => "🖼️",
                ".txt" => "📃",
                ".zip" or ".rar" or ".7z" => "📦",
                _ => "📎"
            };
        }
    }
}
