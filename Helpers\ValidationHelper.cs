using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Controls;

namespace Archif.Helpers
{
    /// <summary>
    /// مساعد التحقق من صحة المدخلات مع دعم اللغة العربية
    /// </summary>
    public static class ValidationHelper
    {
        // أنماط التحقق من صحة البيانات
        private static readonly Regex ArabicTextRegex = new Regex(@"^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\p{P}]+$");
        private static readonly Regex DocumentNumberRegex = new Regex(@"^[\d\u0660-\u0669]+[\/\-]?[\d\u0660-\u0669]*$");
        private static readonly Regex EmailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
        private static readonly Regex PhoneRegex = new Regex(@"^[\+]?[\d\u0660-\u0669\s\-\(\)]{7,15}$");

        /// <summary>
        /// نتيجة التحقق من صحة البيانات
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new List<string>();
            public List<string> Warnings { get; set; } = new List<string>();

            public void AddError(string error)
            {
                IsValid = false;
                Errors.Add(error);
            }

            public void AddWarning(string warning)
            {
                Warnings.Add(warning);
            }

            public string GetErrorsText()
            {
                return string.Join("\n", Errors);
            }

            public string GetWarningsText()
            {
                return string.Join("\n", Warnings);
            }
        }

        /// <summary>
        /// التحقق من صحة النص العربي
        /// </summary>
        public static ValidationResult ValidateArabicText(string text, string fieldName, bool isRequired = true, int minLength = 0, int maxLength = int.MaxValue)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(text))
            {
                if (isRequired)
                {
                    result.AddError($"حقل {fieldName} مطلوب");
                }
                return result;
            }

            // تنظيف النص من المسافات الزائدة
            text = text.Trim();

            // التحقق من الطول
            if (text.Length < minLength)
            {
                result.AddError($"حقل {fieldName} يجب أن يحتوي على {minLength} أحرف على الأقل");
            }

            if (text.Length > maxLength)
            {
                result.AddError($"حقل {fieldName} يجب أن لا يتجاوز {maxLength} حرف");
            }

            // التحقق من احتواء النص على أحرف عربية صحيحة
            if (!ArabicTextRegex.IsMatch(text))
            {
                result.AddWarning($"حقل {fieldName} يحتوي على أحرف غير مدعومة");
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة رقم الوثيقة
        /// </summary>
        public static ValidationResult ValidateDocumentNumber(string documentNumber)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(documentNumber))
            {
                result.AddError("رقم الوثيقة مطلوب");
                return result;
            }

            documentNumber = documentNumber.Trim();

            // التحقق من التنسيق
            if (!DocumentNumberRegex.IsMatch(documentNumber))
            {
                result.AddError("تنسيق رقم الوثيقة غير صحيح. يجب أن يحتوي على أرقام فقط مع إمكانية استخدام / أو -");
            }

            // التحقق من الطول
            if (documentNumber.Length > 50)
            {
                result.AddError("رقم الوثيقة طويل جداً (الحد الأقصى 50 حرف)");
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة التاريخ
        /// </summary>
        public static ValidationResult ValidateDate(DateTime? date, string fieldName, bool isRequired = true, DateTime? minDate = null, DateTime? maxDate = null)
        {
            var result = new ValidationResult { IsValid = true };

            if (!date.HasValue)
            {
                if (isRequired)
                {
                    result.AddError($"حقل {fieldName} مطلوب");
                }
                return result;
            }

            var dateValue = date.Value;

            // التحقق من الحد الأدنى للتاريخ
            if (minDate.HasValue && dateValue < minDate.Value)
            {
                result.AddError($"{fieldName} لا يمكن أن يكون قبل {LocalizationManager.FormatDate(minDate.Value)}");
            }

            // التحقق من الحد الأقصى للتاريخ
            if (maxDate.HasValue && dateValue > maxDate.Value)
            {
                result.AddError($"{fieldName} لا يمكن أن يكون بعد {LocalizationManager.FormatDate(maxDate.Value)}");
            }

            // تحذير إذا كان التاريخ في المستقبل البعيد
            if (dateValue > DateTime.Now.AddYears(1))
            {
                result.AddWarning($"{fieldName} في المستقبل البعيد");
            }

            // تحذير إذا كان التاريخ قديم جداً
            if (dateValue < DateTime.Now.AddYears(-50))
            {
                result.AddWarning($"{fieldName} قديم جداً");
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        public static ValidationResult ValidateEmail(string email, bool isRequired = true)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(email))
            {
                if (isRequired)
                {
                    result.AddError("البريد الإلكتروني مطلوب");
                }
                return result;
            }

            email = email.Trim().ToLower();

            if (!EmailRegex.IsMatch(email))
            {
                result.AddError("تنسيق البريد الإلكتروني غير صحيح");
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف
        /// </summary>
        public static ValidationResult ValidatePhone(string phone, bool isRequired = true)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(phone))
            {
                if (isRequired)
                {
                    result.AddError("رقم الهاتف مطلوب");
                }
                return result;
            }

            phone = phone.Trim();

            if (!PhoneRegex.IsMatch(phone))
            {
                result.AddError("تنسيق رقم الهاتف غير صحيح");
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة الملف
        /// </summary>
        public static ValidationResult ValidateFile(string filePath, string[] allowedExtensions = null, long maxSizeBytes = 50 * 1024 * 1024)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(filePath))
            {
                result.AddError("مسار الملف مطلوب");
                return result;
            }

            if (!File.Exists(filePath))
            {
                result.AddError("الملف غير موجود");
                return result;
            }

            var fileInfo = new FileInfo(filePath);

            // التحقق من حجم الملف
            if (fileInfo.Length > maxSizeBytes)
            {
                var maxSizeMB = maxSizeBytes / (1024 * 1024);
                result.AddError($"حجم الملف كبير جداً (الحد الأقصى {maxSizeMB} ميجابايت)");
            }

            // التحقق من نوع الملف
            if (allowedExtensions != null && allowedExtensions.Length > 0)
            {
                var extension = fileInfo.Extension.ToLower();
                if (!allowedExtensions.Contains(extension))
                {
                    result.AddError($"نوع الملف غير مدعوم. الأنواع المدعومة: {string.Join(", ", allowedExtensions)}");
                }
            }

            // فحص أمني بسيط للملف
            if (IsPotentiallyDangerousFile(filePath))
            {
                result.AddError("الملف قد يحتوي على محتوى خطير");
            }

            return result;
        }

        /// <summary>
        /// التحقق من أن الملف ليس خطيراً
        /// </summary>
        private static bool IsPotentiallyDangerousFile(string filePath)
        {
            var dangerousExtensions = new[] { ".exe", ".bat", ".cmd", ".com", ".scr", ".vbs", ".js" };
            var extension = Path.GetExtension(filePath).ToLower();
            return dangerousExtensions.Contains(extension);
        }

        /// <summary>
        /// التحقق من صحة النموذج بالكامل
        /// </summary>
        public static ValidationResult ValidateForm(params ValidationResult[] validations)
        {
            var result = new ValidationResult { IsValid = true };

            foreach (var validation in validations)
            {
                if (!validation.IsValid)
                {
                    result.IsValid = false;
                    result.Errors.AddRange(validation.Errors);
                }
                result.Warnings.AddRange(validation.Warnings);
            }

            return result;
        }

        /// <summary>
        /// تطبيق نتيجة التحقق على عنصر واجهة المستخدم
        /// </summary>
        public static void ApplyValidationToControl(Control control, ValidationResult validation)
        {
            if (control == null) return;

            if (!validation.IsValid)
            {
                // تطبيق نمط الخطأ
                control.BorderBrush = System.Windows.Media.Brushes.Red;
                control.BorderThickness = new System.Windows.Thickness(2);
                control.ToolTip = validation.GetErrorsText();
            }
            else if (validation.Warnings.Any())
            {
                // تطبيق نمط التحذير
                control.BorderBrush = System.Windows.Media.Brushes.Orange;
                control.BorderThickness = new System.Windows.Thickness(1);
                control.ToolTip = validation.GetWarningsText();
            }
            else
            {
                // تطبيق النمط الطبيعي
                control.BorderBrush = System.Windows.Media.Brushes.LightGray;
                control.BorderThickness = new System.Windows.Thickness(1);
                control.ToolTip = null;
            }
        }

        /// <summary>
        /// تنظيف النص من الأحرف الخطيرة
        /// </summary>
        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // إزالة الأحرف الخطيرة المحتملة
            var dangerous = new[] { "<", ">", "\"", "'", "&", "script", "javascript", "vbscript" };
            var sanitized = input;

            foreach (var danger in dangerous)
            {
                sanitized = sanitized.Replace(danger, "", StringComparison.OrdinalIgnoreCase);
            }

            return sanitized.Trim();
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        public static ValidationResult ValidatePassword(string password)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(password))
            {
                result.AddError("كلمة المرور مطلوبة");
                return result;
            }

            if (password.Length < 8)
            {
                result.AddError("كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل");
            }

            if (!password.Any(char.IsUpper))
            {
                result.AddWarning("يُنصح بوجود حرف كبير واحد على الأقل");
            }

            if (!password.Any(char.IsLower))
            {
                result.AddWarning("يُنصح بوجود حرف صغير واحد على الأقل");
            }

            if (!password.Any(char.IsDigit))
            {
                result.AddWarning("يُنصح بوجود رقم واحد على الأقل");
            }

            if (!password.Any(c => !char.IsLetterOrDigit(c)))
            {
                result.AddWarning("يُنصح بوجود رمز خاص واحد على الأقل");
            }

            return result;
        }
    }
}
