<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>

    <AssemblyTitle>نظام الأرشفة الإلكترونية - إصدار متوافق</AssemblyTitle>
    <AssemblyDescription>نظام احترافي لإدارة الوثائق الصادرة والواردة - متوافق مع Windows 7</AssemblyDescription>
    <AssemblyCompany>شركة التطوير</AssemblyCompany>
    <AssemblyProduct>Archif Legacy</AssemblyProduct>
    <Version>*******</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    
    <!-- Windows 7 Compatibility -->
    <SupportedOSPlatformVersion>6.1</SupportedOSPlatformVersion>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>true</SelfContained>
    <PublishSingleFile>true</PublishSingleFile>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <!-- Entity Framework for .NET Framework -->
    <PackageReference Include="EntityFramework" Version="6.4.4" />
    <PackageReference Include="System.Data.SQLite.EF6" Version="1.0.118" />
    
    <!-- Material Design for .NET Framework -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    
    <!-- Office Integration -->
    <PackageReference Include="ClosedXML" Version="0.95.4" />
    
    <!-- Additional Libraries -->
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- Windows 7 Specific -->
    <PackageReference Include="Microsoft.WindowsAPICodePack-Core" Version="1.1.4" />
    <PackageReference Include="Microsoft.WindowsAPICodePack-Shell" Version="1.1.4" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Resources\" />
    <Folder Include="Helpers\" />
    <Folder Include="Compatibility\" />
  </ItemGroup>

  <!-- Windows 7 Specific Build Configuration -->
  <PropertyGroup Condition="'$(Configuration)'=='Windows7'">
    <DefineConstants>WINDOWS7_COMPAT</DefineConstants>
    <TargetFramework>net48</TargetFramework>
    <OutputPath>bin\Windows7\</OutputPath>
  </PropertyGroup>

</Project>
