<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion. Mimetype corresponds to a base64 encoded 
    serialized .NET Framework object.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Application Info -->
  <data name="AppName" xml:space="preserve">
    <value>نظام الأرشفة الإلكترونية</value>
  </data>
  <data name="AppVersion" xml:space="preserve">
    <value>الإصدار 2.0.0</value>
  </data>
  <data name="AppDescription" xml:space="preserve">
    <value>نظام احترافي لإدارة الوثائق الصادرة والواردة</value>
  </data>
  
  <!-- Navigation -->
  <data name="Nav_Home" xml:space="preserve">
    <value>الرئيسية</value>
  </data>
  <data name="Nav_Departments" xml:space="preserve">
    <value>الأقسام</value>
  </data>
  <data name="Nav_Organizations" xml:space="preserve">
    <value>الجهات</value>
  </data>
  <data name="Nav_Settings" xml:space="preserve">
    <value>الإعدادات</value>
  </data>
  
  <!-- Common Actions -->
  <data name="Action_Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Action_Edit" xml:space="preserve">
    <value>تحرير</value>
  </data>
  <data name="Action_Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Action_Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Action_Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Action_Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="Action_Export" xml:space="preserve">
    <value>تصدير</value>
  </data>
  <data name="Action_Import" xml:space="preserve">
    <value>استيراد</value>
  </data>
  <data name="Action_Refresh" xml:space="preserve">
    <value>تحديث</value>
  </data>
  <data name="Action_View" xml:space="preserve">
    <value>عرض</value>
  </data>
  
  <!-- Messages -->
  <data name="Msg_Success" xml:space="preserve">
    <value>تم بنجاح</value>
  </data>
  <data name="Msg_Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="Msg_Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Msg_Info" xml:space="preserve">
    <value>معلومات</value>
  </data>
  <data name="Msg_Confirm" xml:space="preserve">
    <value>تأكيد</value>
  </data>
  
  <!-- Success Messages -->
  <data name="Msg_AddSuccess" xml:space="preserve">
    <value>تم الإضافة بنجاح</value>
  </data>
  <data name="Msg_UpdateSuccess" xml:space="preserve">
    <value>تم التحديث بنجاح</value>
  </data>
  <data name="Msg_DeleteSuccess" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  <data name="Msg_SaveSuccess" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  
  <!-- Error Messages -->
  <data name="Msg_AddError" xml:space="preserve">
    <value>خطأ في الإضافة</value>
  </data>
  <data name="Msg_UpdateError" xml:space="preserve">
    <value>خطأ في التحديث</value>
  </data>
  <data name="Msg_DeleteError" xml:space="preserve">
    <value>خطأ في الحذف</value>
  </data>
  <data name="Msg_LoadError" xml:space="preserve">
    <value>خطأ في التحميل</value>
  </data>
  <data name="Msg_SaveError" xml:space="preserve">
    <value>خطأ في الحفظ</value>
  </data>
  
  <!-- Confirmation Messages -->
  <data name="Msg_DeleteConfirm" xml:space="preserve">
    <value>هل أنت متأكد من الحذف؟</value>
  </data>
  <data name="Msg_SaveConfirm" xml:space="preserve">
    <value>هل تريد حفظ التغييرات؟</value>
  </data>
  
  <!-- Document Types -->
  <data name="DocType_Incoming" xml:space="preserve">
    <value>وارد</value>
  </data>
  <data name="DocType_Outgoing" xml:space="preserve">
    <value>صادر</value>
  </data>
  
  <!-- Document Fields -->
  <data name="Doc_Number" xml:space="preserve">
    <value>رقم الكتاب</value>
  </data>
  <data name="Doc_Date" xml:space="preserve">
    <value>تاريخ الكتاب</value>
  </data>
  <data name="Doc_Subject" xml:space="preserve">
    <value>موضوع الكتاب</value>
  </data>
  <data name="Doc_Type" xml:space="preserve">
    <value>نوع الكتاب</value>
  </data>
  <data name="Doc_FromTo" xml:space="preserve">
    <value>من/إلى</value>
  </data>
  <data name="Doc_Department" xml:space="preserve">
    <value>القسم</value>
  </data>
  <data name="Doc_Folder" xml:space="preserve">
    <value>الضبارة</value>
  </data>
  <data name="Doc_Organization" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="Doc_ArchiveSequence" xml:space="preserve">
    <value>تسلسل الحفظ</value>
  </data>
  <data name="Doc_Notes" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="Doc_Attachments" xml:space="preserve">
    <value>المرفقات</value>
  </data>
  
  <!-- Statistics -->
  <data name="Stats_TotalDocuments" xml:space="preserve">
    <value>إجمالي الوثائق</value>
  </data>
  <data name="Stats_OutgoingDocuments" xml:space="preserve">
    <value>الكتب الصادرة</value>
  </data>
  <data name="Stats_IncomingDocuments" xml:space="preserve">
    <value>الكتب الواردة</value>
  </data>
  <data name="Stats_ThisMonth" xml:space="preserve">
    <value>هذا الشهر</value>
  </data>
  
  <!-- Search -->
  <data name="Search_Quick" xml:space="preserve">
    <value>البحث السريع</value>
  </data>
  <data name="Search_Advanced" xml:space="preserve">
    <value>البحث المتقدم</value>
  </data>
  <data name="Search_Placeholder" xml:space="preserve">
    <value>البحث السريع (رقم الكتاب، الموضوع، الجهة...)</value>
  </data>
  
  <!-- Filters -->
  <data name="Filter_All" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="Filter_Year" xml:space="preserve">
    <value>السنة</value>
  </data>
  <data name="Filter_FromDate" xml:space="preserve">
    <value>من تاريخ</value>
  </data>
  <data name="Filter_ToDate" xml:space="preserve">
    <value>إلى تاريخ</value>
  </data>
  <data name="Filter_Apply" xml:space="preserve">
    <value>تطبيق الفلاتر</value>
  </data>
  <data name="Filter_Clear" xml:space="preserve">
    <value>مسح الفلاتر</value>
  </data>
  
  <!-- Loading -->
  <data name="Loading_App" xml:space="preserve">
    <value>جاري تحميل التطبيق...</value>
  </data>
  <data name="Loading_Data" xml:space="preserve">
    <value>جاري تحميل البيانات...</value>
  </data>
  <data name="Loading_Please_Wait" xml:space="preserve">
    <value>يرجى الانتظار...</value>
  </data>
</root>
