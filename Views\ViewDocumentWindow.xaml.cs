using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Archif.Models;
using Archif.Services;
using MessageBox = System.Windows.MessageBox;
using Button = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// نافذة عرض تفاصيل الوثيقة
    /// </summary>
    public partial class ViewDocumentWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private readonly FileService _fileService;
        private readonly Document _document;
        private List<Attachment> _attachments;

        public event EventHandler? DocumentUpdated;

        public ViewDocumentWindow(DatabaseService databaseService, Document document)
        {
            InitializeComponent();
            _databaseService = databaseService;
            _fileService = new FileService();
            _document = document;
            _attachments = new List<Attachment>();

            LoadDocumentData();
        }

        private async void LoadDocumentData()
        {
            try
            {
                // تحميل بيانات الوثيقة
                WindowTitleText.Text = $"تفاصيل الوثيقة - {_document.DocumentNumber}";
                DocumentNumberText.Text = _document.DocumentNumber;
                DocumentDateText.Text = _document.DocumentDate.ToString("yyyy/MM/dd");
                DocumentTypeText.Text = _document.Type == DocumentType.Incoming ? "وارد" : "صادر";
                ArchiveSequenceText.Text = _document.ArchiveSequence ?? "غير محدد";
                DepartmentText.Text = _document.Department?.Name ?? "غير محدد";
                FolderText.Text = _document.Folder?.Name ?? "غير محدد";
                OrganizationText.Text = _document.Organization?.Name ?? "غير محدد";
                SubjectText.Text = _document.Subject;
                CreatedDateText.Text = _document.CreatedDate.ToString("yyyy/MM/dd HH:mm:ss");

                // تحميل المرفقات
                await LoadAttachments();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadAttachments()
        {
            try
            {
                _attachments = await _databaseService.GetAttachmentsByDocumentIdAsync(_document.Id);

                var attachmentViewModels = _attachments.Select(a => new AttachmentViewModel
                {
                    Id = a.Id,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    FileType = a.FileType,
                    MimeType = a.MimeType,
                    FileSize = a.FileSize,
                    Type = a.Type,
                    Description = a.Description,
                    CreatedDate = a.CreatedDate,
                    FileIcon = GetFileIcon(a.FileType),
                    TypeDisplay = GetTypeDisplay(a.Type),
                    FileSizeFormatted = _fileService.GetFormattedFileSize(a.FileSize),
                    CreatedDateFormatted = a.CreatedDate.ToString("yyyy/MM/dd")
                }).ToList();

                AttachmentsListBox.ItemsSource = attachmentViewModels;
                AttachmentsCountText.Text = $"({_attachments.Count} ملف)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المرفقات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                AttachmentsListBox.ItemsSource = new List<AttachmentViewModel>();
                AttachmentsCountText.Text = "(0 ملف)";
            }
        }

        private string GetFileIcon(string fileType)
        {
            return fileType.ToLower() switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" => "🖼️",
                ".txt" => "📃",
                ".zip" or ".rar" or ".7z" => "📦",
                _ => "📎"
            };
        }

        private string GetTypeDisplay(AttachmentType type)
        {
            return type switch
            {
                AttachmentType.Document => "وثيقة",
                AttachmentType.ScannedImage => "صورة ممسوحة",
                AttachmentType.ScannedPdf => "PDF ممسوح",
                _ => "غير محدد"
            };
        }

        private async void PreviewAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is AttachmentViewModel attachment)
            {
                try
                {
                    if (!File.Exists(attachment.FilePath))
                    {
                        MessageBox.Show("الملف غير موجود. قد يكون تم حذفه أو نقله.", "ملف غير موجود",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    await _fileService.OpenAttachmentAsync(attachment.FilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في معاينة الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void OpenAttachment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is AttachmentViewModel attachment)
            {
                try
                {
                    if (!File.Exists(attachment.FilePath))
                    {
                        MessageBox.Show("الملف غير موجود. قد يكون تم حذفه أو نقله.", "ملف غير موجود",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    await _fileService.OpenAttachmentAsync(attachment.FilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void EditDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new EditDocumentWindow(_databaseService, _document);
                editWindow.DocumentUpdated += (s, args) =>
                {
                    DocumentUpdated?.Invoke(this, EventArgs.Empty);
                    LoadDocumentData(); // إعادة تحميل البيانات
                };
                editWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء تقرير طباعة بسيط
                var printContent = GeneratePrintContent();

                // حفظ في ملف مؤقت وفتحه للطباعة
                var tempFile = Path.Combine(Path.GetTempPath(), $"Document_{_document.Id}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                File.WriteAllText(tempFile, printContent);

                Process.Start(new ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GeneratePrintContent()
        {
            var html = $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>تفاصيل الوثيقة - {_document.DocumentNumber}</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }}
        .header {{ text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }}
        .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
        .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
        .info-table th {{ background-color: #f2f2f2; }}
        .attachments {{ margin-top: 20px; }}
        .attachment-item {{ margin: 5px 0; padding: 5px; border: 1px solid #eee; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>تفاصيل الوثيقة</h1>
        <h2>{_document.DocumentNumber}</h2>
    </div>

    <table class='info-table'>
        <tr><th>رقم الوثيقة</th><td>{_document.DocumentNumber}</td></tr>
        <tr><th>تاريخ الوثيقة</th><td>{_document.DocumentDate:yyyy/MM/dd}</td></tr>
        <tr><th>نوع الوثيقة</th><td>{(_document.Type == DocumentType.Incoming ? "وارد" : "صادر")}</td></tr>
        <tr><th>تسلسل الحفظ</th><td>{_document.ArchiveSequence ?? "غير محدد"}</td></tr>
        <tr><th>القسم</th><td>{_document.Department?.Name ?? "غير محدد"}</td></tr>
        <tr><th>الضبارة</th><td>{_document.Folder?.Name ?? "غير محدد"}</td></tr>
        <tr><th>الجهة</th><td>{_document.Organization?.Name ?? "غير محدد"}</td></tr>
        <tr><th>الموضوع</th><td>{_document.Subject}</td></tr>
        <tr><th>تاريخ الإنشاء</th><td>{_document.CreatedDate:yyyy/MM/dd HH:mm:ss}</td></tr>
    </table>

    <div class='attachments'>
        <h3>المرفقات ({_attachments.Count} ملف)</h3>";

            foreach (var attachment in _attachments)
            {
                html += $@"
        <div class='attachment-item'>
            <strong>{attachment.FileName}</strong> - {GetTypeDisplay(attachment.Type)} - {_fileService.GetFormattedFileSize(attachment.FileSize)}
        </div>";
            }

            html += @"
    </div>

    <div style='margin-top: 30px; text-align: center; font-size: 12px; color: #666;'>
        تم إنشاء هذا التقرير في " + DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss") + @"
    </div>
</body>
</html>";

            return html;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    /// <summary>
    /// نموذج عرض المرفق
    /// </summary>
    public class AttachmentViewModel
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public AttachmentType Type { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public string FileIcon { get; set; } = string.Empty;
        public string TypeDisplay { get; set; } = string.Empty;
        public string FileSizeFormatted { get; set; } = string.Empty;
        public string CreatedDateFormatted { get; set; } = string.Empty;
    }
}
