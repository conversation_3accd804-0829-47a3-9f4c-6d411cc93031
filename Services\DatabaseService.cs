using Microsoft.EntityFrameworkCore;
using Archif.Data;
using Archif.Models;
using System.IO;

namespace Archif.Services
{
    public class DatabaseService
    {
        private readonly ArchifDbContext _context;

        public DatabaseService()
        {
            _context = new ArchifDbContext();
            InitializeDatabase();
        }

        public void InitializeDatabase()
        {
            try
            {
                // التأكد من وجود المجلد
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                        "Archif", "archif.db");
                var directory = Path.GetDirectoryName(dbPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // إنشاء قاعدة البيانات بدون عرض رسائل
                _context.Database.EnsureCreated();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", ex);
            }
        }

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                // التأكد من وجود المجلد
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                        "Archif", "archif.db");
                var directory = Path.GetDirectoryName(dbPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // إنشاء قاعدة البيانات
                var created = await _context.Database.EnsureCreatedAsync();

                // التحقق من وجود جدول الروابط وإنشاؤه إذا لم يكن موجوداً
                await EnsureHyperlinksTableExistsAsync();

                // التحقق من وجود جدول الجهات وإنشاؤه إذا لم يكن موجوداً
                await EnsureOrganizationsTableExistsAsync();

                // إضافة بيانات تجريبية إذا كانت قاعدة البيانات جديدة
                if (created)
                {
                    await SeedInitialDataAsync();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        private async Task EnsureHyperlinksTableExistsAsync()
        {
            try
            {
                // محاولة الوصول لجدول الروابط
                var count = await _context.DocumentHyperlinks.CountAsync();
            }
            catch
            {
                // إذا فشل، فهذا يعني أن الجدول غير موجود، لذا سننشئه
                await _context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE IF NOT EXISTS DocumentHyperlinks (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        DocumentId INTEGER NOT NULL,
                        Title TEXT NOT NULL,
                        Url TEXT NOT NULL,
                        Description TEXT,
                        CreatedDate TEXT NOT NULL,
                        FOREIGN KEY (DocumentId) REFERENCES Documents (Id) ON DELETE CASCADE
                    )");
            }
        }

        private async Task EnsureOrganizationsTableExistsAsync()
        {
            try
            {
                // محاولة الوصول لجدول الجهات
                var count = await _context.Organizations.CountAsync();
            }
            catch
            {
                // إذا فشل، فهذا يعني أن الجدول غير موجود، لذا سننشئه
                await _context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE IF NOT EXISTS Organizations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        Type INTEGER NOT NULL DEFAULT 0,
                        CreatedDate TEXT NOT NULL,
                        IsActive INTEGER NOT NULL DEFAULT 1
                    )");

                // إضافة عمود OrganizationId لجدول Documents إذا لم يكن موجوداً
                try
                {
                    await _context.Database.ExecuteSqlRawAsync(@"
                        ALTER TABLE Documents ADD COLUMN OrganizationId INTEGER");
                }
                catch
                {
                    // العمود موجود بالفعل، لا نفعل شيئاً
                }
            }
        }

        private async Task SeedInitialDataAsync()
        {
            try
            {
                // إضافة أقسام افتراضية
                if (!await _context.Departments.AnyAsync())
                {
                    var departments = new[]
                    {
                        new Department { Name = "الإدارة العامة", IsActive = true },
                        new Department { Name = "الشؤون المالية", IsActive = true },
                        new Department { Name = "الموارد البشرية", IsActive = true },
                        new Department { Name = "الشؤون القانونية", IsActive = true }
                    };

                    _context.Departments.AddRange(departments);
                    await _context.SaveChangesAsync();

                    // إضافة ضبائر افتراضية
                    var folders = new[]
                    {
                        new Folder { Name = "الكتب الصادرة", DepartmentId = 1, IsActive = true },
                        new Folder { Name = "الكتب الواردة", DepartmentId = 1, IsActive = true },
                        new Folder { Name = "الميزانية", DepartmentId = 2, IsActive = true },
                        new Folder { Name = "التوظيف", DepartmentId = 3, IsActive = true }
                    };

                    _context.Folders.AddRange(folders);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل إضافة البيانات الأولية، لا نوقف التطبيق
                // فقط نسجل الخطأ في Debug
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إضافة البيانات الأولية: {ex.Message}");
            }
        }

        // إحصائيات للوحة الرئيسية
        public async Task<int> GetDepartmentsCountAsync()
        {
            return await _context.Departments.CountAsync(d => d.IsActive);
        }

        public async Task<int> GetIncomingDocumentsCountAsync()
        {
            return await _context.Documents.CountAsync(d => d.Type == DocumentType.Incoming && !d.IsDeleted);
        }

        public async Task<int> GetOutgoingDocumentsCountAsync()
        {
            return await _context.Documents.CountAsync(d => d.Type == DocumentType.Outgoing && !d.IsDeleted);
        }

        public async Task<int> GetFoldersCountAsync()
        {
            return await _context.Folders.CountAsync(f => f.IsActive);
        }

        // إدارة الأقسام
        public async Task<List<Department>> GetDepartmentsAsync()
        {
            return await _context.Departments
                .Where(d => d.IsActive)
                .OrderBy(d => d.Name)
                .ToListAsync();
        }

        public async Task<Department> AddDepartmentAsync(string name)
        {
            var department = new Department { Name = name };
            _context.Departments.Add(department);
            await _context.SaveChangesAsync();
            return department;
        }

        // إدارة الضبائر
        public async Task<List<Folder>> GetFoldersByDepartmentAsync(int departmentId)
        {
            return await _context.Folders
                .Where(f => f.DepartmentId == departmentId && f.IsActive)
                .OrderBy(f => f.Name)
                .ToListAsync();
        }

        public async Task<List<Folder>> GetFoldersByDepartmentIdAsync(int departmentId)
        {
            return await _context.Folders
                .Where(f => f.DepartmentId == departmentId && f.IsActive)
                .OrderBy(f => f.Name)
                .ToListAsync();
        }

        public async Task<List<Folder>> GetAllFoldersAsync()
        {
            return await _context.Folders
                .Include(f => f.Department)
                .Where(f => f.IsActive)
                .OrderBy(f => f.Department.Name)
                .ThenBy(f => f.Name)
                .ToListAsync();
        }

        public async Task<Folder> AddFolderAsync(string name, int departmentId)
        {
            var folder = new Folder { Name = name, DepartmentId = departmentId };
            _context.Folders.Add(folder);
            await _context.SaveChangesAsync();
            return folder;
        }

        // إدارة الوثائق
        public async Task<int> GetNextSequenceNumberAsync()
        {
            var lastDocument = await _context.Documents
                .OrderByDescending(d => d.SequenceNumber)
                .FirstOrDefaultAsync();

            return (lastDocument?.SequenceNumber ?? 0) + 1;
        }

        public async Task<Document> AddDocumentAsync(Document document)
        {
            document.SequenceNumber = await GetNextSequenceNumberAsync();
            document.ArchiveSequence = GenerateArchiveSequence(document);

            _context.Documents.Add(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public async Task<List<Document>> GetDocumentsAsync()
        {
            return await _context.Documents
                .Include(d => d.Department)
                .Include(d => d.Folder)
                .Include(d => d.Attachments)
                .Where(d => !d.IsDeleted)
                .OrderByDescending(d => d.CreatedDate)
                .ToListAsync();
        }

        public async Task<Document?> GetDocumentByIdAsync(int documentId)
        {
            return await _context.Documents
                .Include(d => d.Department)
                .Include(d => d.Folder)
                .Include(d => d.Organization)
                .Include(d => d.Attachments)
                .FirstOrDefaultAsync(d => d.Id == documentId && !d.IsDeleted);
        }

        public async Task UpdateDocumentAsync(Document document)
        {
            _context.Documents.Update(document);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteDocumentAsync(int documentId)
        {
            var document = await _context.Documents.FindAsync(documentId);
            if (document != null)
            {
                document.IsDeleted = true;
                await _context.SaveChangesAsync();
            }
        }

        private string GenerateArchiveSequence(Document document)
        {
            var year = document.DocumentDate.Year;
            var typePrefix = document.Type == DocumentType.Incoming ? "W" : "S";
            return $"{typePrefix}-{year}-{document.SequenceNumber:D4}";
        }

        // طرق إضافية للأقسام والضبائر
        public async Task UpdateDepartmentAsync(Department department)
        {
            _context.Departments.Update(department);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteDepartmentAsync(int departmentId)
        {
            var department = await _context.Departments.FindAsync(departmentId);
            if (department != null)
            {
                department.IsActive = false;
                await _context.SaveChangesAsync();
            }
        }

        public async Task UpdateFolderAsync(Folder folder)
        {
            _context.Folders.Update(folder);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteFolderAsync(int folderId)
        {
            var folder = await _context.Folders.FindAsync(folderId);
            if (folder != null)
            {
                folder.IsActive = false;
                await _context.SaveChangesAsync();
            }
        }

        // طرق النسخ الاحتياطي
        public Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                        "Archif", "archif.db");

                if (File.Exists(dbPath))
                {
                    File.Copy(dbPath, backupPath, true);
                    return Task.FromResult(true);
                }
                return Task.FromResult(false);
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        public async Task<bool> RestoreBackupAsync(string backupPath)
        {
            try
            {
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                        "Archif", "archif.db");

                if (File.Exists(backupPath))
                {
                    // إغلاق الاتصال الحالي
                    await _context.Database.CloseConnectionAsync();

                    // نسخ ملف النسخة الاحتياطية
                    File.Copy(backupPath, dbPath, true);

                    // إعادة تهيئة قاعدة البيانات
                    await _context.Database.OpenConnectionAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        // طرق إدارة الروابط
        public async Task AddDocumentHyperlinkAsync(DocumentHyperlink hyperlink)
        {
            _context.DocumentHyperlinks.Add(hyperlink);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<DocumentHyperlink>> GetDocumentHyperlinksAsync(int documentId)
        {
            return await _context.DocumentHyperlinks
                .Where(h => h.DocumentId == documentId)
                .OrderBy(h => h.CreatedDate)
                .ToListAsync();
        }

        public async Task UpdateDocumentHyperlinkAsync(DocumentHyperlink hyperlink)
        {
            _context.DocumentHyperlinks.Update(hyperlink);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteDocumentHyperlinkAsync(int hyperlinkId)
        {
            var hyperlink = await _context.DocumentHyperlinks.FindAsync(hyperlinkId);
            if (hyperlink != null)
            {
                _context.DocumentHyperlinks.Remove(hyperlink);
                await _context.SaveChangesAsync();
            }
        }

        // طرق إدارة الجهات
        public async Task<List<Organization>> GetOrganizationsAsync()
        {
            return await _context.Organizations
                .Where(o => o.IsActive)
                .OrderBy(o => o.Name)
                .ToListAsync();
        }

        public async Task<Organization> AddOrganizationAsync(Organization organization)
        {
            _context.Organizations.Add(organization);
            await _context.SaveChangesAsync();
            return organization;
        }

        public async Task UpdateOrganizationAsync(Organization organization)
        {
            _context.Organizations.Update(organization);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteOrganizationAsync(int organizationId)
        {
            var organization = await _context.Organizations.FindAsync(organizationId);
            if (organization != null)
            {
                organization.IsActive = false;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<Organization?> GetOrganizationByIdAsync(int organizationId)
        {
            return await _context.Organizations.FindAsync(organizationId);
        }

        public async Task<int> GetOrganizationsCountAsync()
        {
            return await _context.Organizations.CountAsync(o => o.IsActive);
        }

        public async Task<int> GetDocumentsByOrganizationCountAsync(int organizationId)
        {
            return await _context.Documents.CountAsync(d => d.OrganizationId == organizationId && !d.IsDeleted);
        }

        public async Task<bool> IsOrganizationNameExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.Organizations.Where(o => o.IsActive && o.Name.ToLower() == name.ToLower());

            if (excludeId.HasValue)
            {
                query = query.Where(o => o.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        public string GetDatabasePath()
        {
            return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                              "Archif", "archif.db");
        }

        // طرق الإحصائيات والعدادات الإضافية
        public async Task<int> GetDocumentsCountAsync()
        {
            return await _context.Documents.CountAsync(d => !d.IsDeleted);
        }

        public async Task<int> GetFoldersByDepartmentCountAsync(int departmentId)
        {
            return await _context.Folders.CountAsync(f => f.DepartmentId == departmentId && f.IsActive);
        }

        public async Task<int> GetDocumentsByDepartmentCountAsync(int departmentId)
        {
            return await _context.Documents.CountAsync(d => d.DepartmentId == departmentId && !d.IsDeleted);
        }

        public async Task<int> GetDocumentsByFolderCountAsync(int folderId)
        {
            return await _context.Documents.CountAsync(d => d.FolderId == folderId && !d.IsDeleted);
        }

        public async Task<Department?> GetDepartmentByIdAsync(int departmentId)
        {
            return await _context.Departments.FindAsync(departmentId);
        }

        public async Task<Folder?> GetFolderByIdAsync(int folderId)
        {
            return await _context.Folders
                .Include(f => f.Department)
                .FirstOrDefaultAsync(f => f.Id == folderId);
        }

        public async Task<int> GetThisMonthDocumentsCountAsync()
        {
            var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            return await _context.Documents.CountAsync(d =>
                d.DocumentDate >= startOfMonth &&
                d.DocumentDate <= endOfMonth &&
                !d.IsDeleted);
        }

        /// <summary>
        /// طريقة محسنة لتحميل الأقسام مع الإحصائيات في استعلام واحد
        /// </summary>
        public async Task<List<DepartmentWithStats>> GetDepartmentsWithStatsAsync()
        {
            var departments = await _context.Departments
                .Where(d => d.IsActive)
                .Select(d => new DepartmentWithStats
                {
                    Id = d.Id,
                    Name = d.Name,
                    CreatedDate = d.CreatedDate,
                    IsActive = d.IsActive,
                    FoldersCount = _context.Folders.Count(f => f.DepartmentId == d.Id && f.IsActive),
                    DocumentsCount = _context.Documents.Count(doc => doc.DepartmentId == d.Id && !doc.IsDeleted)
                })
                .OrderBy(d => d.Name)
                .ToListAsync();

            return departments;
        }

        // طرق البحث والفلترة المتقدمة
        public async Task<(List<Document> Documents, int TotalCount)> GetDocumentsPagedAsync(
            int pageNumber,
            int pageSize,
            string? searchText = null,
            int? departmentId = null,
            int? folderId = null,
            int? organizationId = null,
            DocumentType? documentType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? documentNumber = null)
        {
            var query = _context.Documents
                .Include(d => d.Department)
                .Include(d => d.Folder)
                .Include(d => d.Organization)
                .Where(d => !d.IsDeleted);

            // تطبيق الفلاتر
            if (!string.IsNullOrEmpty(searchText))
            {
                var search = searchText.ToLower();
                query = query.Where(d =>
                    d.Subject.ToLower().Contains(search) ||
                    d.DocumentNumber.ToLower().Contains(search) ||
                    (d.Organization != null && d.Organization.Name.ToLower().Contains(search)) ||
                    d.ArchiveSequence.ToLower().Contains(search));
            }

            if (departmentId.HasValue)
                query = query.Where(d => d.DepartmentId == departmentId.Value);

            if (folderId.HasValue)
                query = query.Where(d => d.FolderId == folderId.Value);

            if (organizationId.HasValue)
                query = query.Where(d => d.OrganizationId == organizationId.Value);

            if (documentType.HasValue)
                query = query.Where(d => d.Type == documentType.Value);

            if (fromDate.HasValue)
                query = query.Where(d => d.DocumentDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(d => d.DocumentDate <= toDate.Value);

            if (!string.IsNullOrEmpty(documentNumber))
                query = query.Where(d => d.DocumentNumber.ToLower().Contains(documentNumber.ToLower()));

            var totalCount = await query.CountAsync();

            var documents = await query
                .OrderByDescending(d => d.DocumentDate)
                .ThenByDescending(d => d.CreatedDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (documents, totalCount);
        }

        // طرق إدارة المرفقات والملفات
        public async Task<Attachment> AddAttachmentAsync(Attachment attachment)
        {
            _context.Attachments.Add(attachment);
            await _context.SaveChangesAsync();
            return attachment;
        }

        public async Task<List<Attachment>> GetAttachmentsByDocumentIdAsync(int documentId)
        {
            return await _context.Attachments
                .Where(a => a.DocumentId == documentId && !a.IsDeleted)
                .OrderBy(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<Attachment?> GetAttachmentByIdAsync(int attachmentId)
        {
            return await _context.Attachments
                .FirstOrDefaultAsync(a => a.Id == attachmentId && !a.IsDeleted);
        }

        public async Task DeleteAttachmentAsync(int attachmentId)
        {
            var attachment = await _context.Attachments.FindAsync(attachmentId);
            if (attachment != null)
            {
                attachment.IsDeleted = true;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> DeleteAttachmentFileAsync(int attachmentId)
        {
            try
            {
                var attachment = await GetAttachmentByIdAsync(attachmentId);
                if (attachment != null && File.Exists(attachment.FilePath))
                {
                    File.Delete(attachment.FilePath);
                    await DeleteAttachmentAsync(attachmentId);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<int> GetAttachmentsCountByDocumentIdAsync(int documentId)
        {
            return await _context.Attachments
                .CountAsync(a => a.DocumentId == documentId && !a.IsDeleted);
        }

        public async Task<long> GetTotalAttachmentsSizeAsync()
        {
            return await _context.Attachments
                .Where(a => !a.IsDeleted)
                .SumAsync(a => a.FileSize);
        }

        public async Task<List<Attachment>> GetAttachmentsByTypeAsync(AttachmentType type)
        {
            return await _context.Attachments
                .Where(a => a.Type == type && !a.IsDeleted)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// كلاس مساعد لتحميل الأقسام مع الإحصائيات
    /// </summary>
    public class DepartmentWithStats
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
        public int FoldersCount { get; set; }
        public int DocumentsCount { get; set; }
    }
}
