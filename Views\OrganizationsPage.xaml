<UserControl x:Class="Archif.Views.OrganizationsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI, Tahoma, Arial"
             Background="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Padding="30,20" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Ellipse Width="28" Height="28" Fill="#4A90E2" VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة الجهات"
                              FontSize="24" FontWeight="Bold"
                              Margin="15,0,0,0" VerticalAlignment="Center"
                              Foreground="#333333"/>
                </StackPanel>

                <Button Grid.Column="1" x:Name="AddOrganizationButton"
                       Background="#4A90E2" Foreground="White"
                       BorderThickness="0" Padding="20,10"
                       Click="AddOrganizationButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <Ellipse Width="16" Height="16" Fill="White" VerticalAlignment="Center"/>
                        <TextBlock Text="إضافة جهة جديدة" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Border Grid.Row="1" Margin="20,20,20,0" Padding="20" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الجهات" FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalOrganizationsText" Text="0" FontSize="24" FontWeight="Bold" 
                              Foreground="#4A90E2" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="الجهات الحكومية" FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="GovernmentOrganizationsText" Text="0" FontSize="24" FontWeight="Bold" 
                              Foreground="#28A745" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="الجهات الخاصة" FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="PrivateOrganizationsText" Text="0" FontSize="24" FontWeight="Bold" 
                              Foreground="#FFC107" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- قائمة الجهات -->
        <Border Grid.Row="2" Margin="20" Padding="20" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان القائمة -->
                <TextBlock Grid.Row="0" Text="قائمة الجهات"
                          FontSize="18" FontWeight="Bold"
                          Margin="0,0,0,15" Foreground="#333333"/>

                <!-- شريط البحث والفلاتر -->
                <Grid Grid.Row="1" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0" x:Name="SearchTextBox"
                            Height="35" Padding="10,8"
                            BorderBrush="#E0E0E0" BorderThickness="1"
                            Margin="0,0,10,0"
                            TextChanged="SearchTextBox_TextChanged">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                    <VisualBrush.Visual>
                                                        <TextBlock Text="البحث في الجهات..." Foreground="#999999" Margin="5,0"/>
                                                    </VisualBrush.Visual>
                                                </VisualBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <ComboBox Grid.Column="1" x:Name="TypeFilterComboBox"
                             Height="35" Padding="10,8"
                             BorderBrush="#E0E0E0" BorderThickness="1"
                             Margin="0,0,10,0"
                             SelectionChanged="TypeFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                        <ComboBoxItem Content="حكومية"/>
                        <ComboBoxItem Content="خاصة"/>
                        <ComboBoxItem Content="أكاديمية"/>
                        <ComboBoxItem Content="غير ربحية"/>
                        <ComboBoxItem Content="دولية"/>
                    </ComboBox>

                    <Button Grid.Column="2" x:Name="RefreshButton"
                           Background="White" Foreground="#666666"
                           BorderBrush="#E0E0E0" BorderThickness="1"
                           Padding="10,8" Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="16" Height="16" Fill="#666666" VerticalAlignment="Center"/>
                            <TextBlock Text="تحديث" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- جدول الجهات -->
                <DataGrid Grid.Row="2" x:Name="OrganizationsDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single"
                         Background="White"
                         BorderThickness="0"
                         RowHeight="50">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الجهة" 
                                          Binding="{Binding Name}" 
                                          Width="*"/>
                        <DataGridTextColumn Header="النوع" 
                                          Binding="{Binding TypeDisplay}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="تاريخ الإضافة" 
                                          Binding="{Binding CreatedDate, StringFormat='{}{0:yyyy/MM/dd}'}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="عدد الوثائق" 
                                          Binding="{Binding DocumentsCount}" 
                                          Width="100"/>
                        <DataGridTemplateColumn Header="العمليات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="تحرير" 
                                               Background="Transparent" 
                                               Foreground="#4A90E2" 
                                               BorderThickness="0" 
                                               Padding="8,4" 
                                               Margin="2"
                                               Click="EditOrganization_Click"
                                               Tag="{Binding}"/>
                                        <Button Content="حذف" 
                                               Background="Transparent" 
                                               Foreground="#DC3545" 
                                               BorderThickness="0" 
                                               Padding="8,4" 
                                               Margin="2"
                                               Click="DeleteOrganization_Click"
                                               Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
