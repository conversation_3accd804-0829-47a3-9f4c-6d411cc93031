<Window x:Class="Archif.Views.AddEditOrganizationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تحرير جهة" 
        Height="450" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="#F5F5F5"
        ResizeMode="NoResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Padding="20,15" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="24" Height="24" Fill="#4A90E2" VerticalAlignment="Center"/>
                <TextBlock x:Name="TitleTextBlock" Text="إضافة جهة جديدة" 
                          FontSize="18" FontWeight="Bold" 
                          Margin="10,0,0,0" VerticalAlignment="Center"
                          Foreground="#333333"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Border Grid.Row="1" Margin="20" Padding="30" 
               Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
            <StackPanel>
                <!-- اسم الجهة -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="اسم الجهة *" 
                              Margin="0,0,0,8" FontWeight="Bold" Foreground="#333333"/>
                    <TextBox x:Name="NameTextBox" 
                            Height="40" Padding="12,10"
                            FontSize="14"
                            BorderBrush="#E0E0E0" BorderThickness="1"
                            Background="White"/>
                    <TextBlock x:Name="NameErrorText" 
                              Text="" Foreground="#DC3545" 
                              FontSize="12" Margin="0,5,0,0"
                              Visibility="Collapsed"/>
                </StackPanel>

                <!-- نوع الجهة -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="نوع الجهة *" 
                              Margin="0,0,0,8" FontWeight="Bold" Foreground="#333333"/>
                    <ComboBox x:Name="TypeComboBox" 
                             Height="40" Padding="12,10"
                             FontSize="14"
                             BorderBrush="#E0E0E0" BorderThickness="1"
                             Background="White">
                        <ComboBoxItem Content="حكومية" Tag="Government"/>
                        <ComboBoxItem Content="خاصة" Tag="Private"/>
                        <ComboBoxItem Content="أكاديمية" Tag="Academic"/>
                        <ComboBoxItem Content="غير ربحية" Tag="NonProfit"/>
                        <ComboBoxItem Content="دولية" Tag="International"/>
                    </ComboBox>
                </StackPanel>

                <!-- الوصف -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="الوصف (اختياري)" 
                              Margin="0,0,0,8" FontWeight="Bold" Foreground="#333333"/>
                    <TextBox x:Name="DescriptionTextBox" 
                            Height="100" Padding="12,10"
                            FontSize="14"
                            TextWrapping="Wrap" AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            BorderBrush="#E0E0E0" BorderThickness="1"
                            Background="White"/>
                </StackPanel>

                <!-- معلومات إضافية -->
                <Border Padding="15" Background="#F8F9FA" 
                       BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="4">
                    <StackPanel>
                        <TextBlock Text="ملاحظات:" FontWeight="Bold" 
                                  Foreground="#666666" Margin="0,0,0,8"/>
                        <TextBlock Text="• اسم الجهة مطلوب ولا يمكن أن يكون فارغاً" 
                                  Foreground="#666666" FontSize="12" Margin="0,2"/>
                        <TextBlock Text="• لا يمكن تكرار اسم الجهة" 
                                  Foreground="#666666" FontSize="12" Margin="0,2"/>
                        <TextBlock Text="• يمكن تعديل جميع البيانات لاحقاً" 
                                  Foreground="#666666" FontSize="12" Margin="0,2"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" 
               Background="White"
               BorderThickness="0,1,0,0"
               BorderBrush="#E0E0E0"
               Padding="20,15">
            
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center">
                <Button x:Name="SaveButton"
                       Content="حفظ"
                       Background="#4A90E2"
                       Foreground="White"
                       BorderThickness="0"
                       Width="120"
                       Height="40"
                       FontSize="14"
                       Margin="10,0"
                       Click="SaveButton_Click"/>

                <Button x:Name="CancelButton"
                       Content="إلغاء"
                       Background="White"
                       Foreground="#666666"
                       BorderBrush="#E0E0E0"
                       BorderThickness="1"
                       Width="120"
                       Height="40"
                       FontSize="14"
                       Margin="10,0"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
