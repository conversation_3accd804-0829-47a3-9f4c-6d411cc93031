using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Archif.Services;
using Archif.Models;
using Archif.Helpers;
using ClosedXML.Excel;
using WpfMessageBox = System.Windows.MessageBox;
using WpfSaveFileDialog = Microsoft.Win32.SaveFileDialog;
using Button = System.Windows.Controls.Button;

namespace Archif.Views
{
    /// <summary>
    /// الصفحة الرئيسية مع إدارة الوثائق المتكاملة
    /// </summary>
    public partial class HomePage : System.Windows.Controls.UserControl, IDisposable
    {
        private readonly DatabaseService _databaseService = null!;
        private readonly ResourceManager _resourceManager;
        private int _currentPage = 1;
        private int _pageSize = 25;
        private int _totalRecords = 0;
        private int _totalPages = 0;
        private bool _disposed = false;

        public HomePage(DatabaseService databaseService)
        {
            try
            {
                InitializeComponent();
                _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
                _resourceManager = new ResourceManager();

                // تعيين القيم الافتراضية
                TypeFilterComboBox.SelectedIndex = 0;
                PageSizeComboBox.SelectedIndex = 1; // 25 عنصر

                // تحميل البيانات بشكل آمن
                Loaded += HomePage_Loaded;
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, LocalizationManager.GetString("Msg_LoadError"));
            }
        }

        private async void HomePage_Loaded(object sender, RoutedEventArgs e)
        {
            await ErrorHandler.ExecuteWithErrorHandlingAsync(LoadData, "تحميل الصفحة الرئيسية");
        }

        private async Task LoadData()
        {
            try
            {
                await LoadStatistics();
                await LoadFilters();
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStatistics()
        {
            try
            {
                var totalDocuments = await _databaseService.GetDocumentsCountAsync();
                var outgoingDocuments = await _databaseService.GetOutgoingDocumentsCountAsync();
                var incomingDocuments = await _databaseService.GetIncomingDocumentsCountAsync();
                var thisMonthDocuments = await _databaseService.GetThisMonthDocumentsCountAsync();

                TotalDocumentsText.Text = totalDocuments.ToString();
                OutgoingCountText.Text = outgoingDocuments.ToString();
                IncomingCountText.Text = incomingDocuments.ToString();
                ThisMonthCountText.Text = thisMonthDocuments.ToString();
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، عرض قيم افتراضية
                TotalDocumentsText.Text = "0";
                OutgoingCountText.Text = "0";
                IncomingCountText.Text = "0";
                ThisMonthCountText.Text = "0";

                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");
                // عدم عرض رسالة خطأ للمستخدم لتجنب الإزعاج
            }
        }

        private async Task LoadFilters()
        {
            try
            {
                // تحميل السنوات
                var currentYear = DateTime.Now.Year;
                YearFilterComboBox.Items.Clear();
                YearFilterComboBox.Items.Add(new ComboBoxItem { Content = "الكل", Tag = null });

                for (int year = currentYear; year >= currentYear - 10; year--)
                {
                    YearFilterComboBox.Items.Add(new ComboBoxItem { Content = year.ToString(), Tag = year });
                }
                YearFilterComboBox.SelectedIndex = 0;

                // تحميل الأقسام
                var departments = await _databaseService.GetDepartmentsAsync();
                DepartmentFilterComboBox.Items.Clear();
                DepartmentFilterComboBox.Items.Add(new ComboBoxItem { Content = "الكل", Tag = null });

                foreach (var department in departments)
                {
                    DepartmentFilterComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = department.Name,
                        Tag = department.Id
                    });
                }
                DepartmentFilterComboBox.SelectedIndex = 0;

                // تحميل الضبارات
                var folders = await _databaseService.GetAllFoldersAsync();
                FolderFilterComboBox.Items.Clear();
                FolderFilterComboBox.Items.Add(new ComboBoxItem { Content = "الكل", Tag = null });

                foreach (var folder in folders)
                {
                    FolderFilterComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = $"{folder.Name} ({folder.Department?.Name})",
                        Tag = folder.Id
                    });
                }
                FolderFilterComboBox.SelectedIndex = 0;

                // تحميل الجهات
                var organizations = await _databaseService.GetOrganizationsAsync();
                OrganizationFilterComboBox.Items.Clear();
                OrganizationFilterComboBox.Items.Add(new ComboBoxItem { Content = "الكل", Tag = null });

                foreach (var organization in organizations)
                {
                    OrganizationFilterComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = organization.Name,
                        Tag = organization.Id
                    });
                }
                OrganizationFilterComboBox.SelectedIndex = 0;

                // تعيين النوع الافتراضي
                TypeFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحميل الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadDocuments()
        {
            try
            {
                var searchText = QuickSearchTextBox?.Text;
                var departmentId = GetSelectedDepartmentId();
                var folderId = GetSelectedFolderId();
                var organizationId = GetSelectedOrganizationId();
                var documentType = GetSelectedDocumentType();
                var fromDate = FromDatePicker?.SelectedDate;
                var toDate = ToDatePicker?.SelectedDate;
                var documentNumber = DocumentNumberFilterTextBox?.Text;

                var (documents, totalCount) = await _databaseService.GetDocumentsPagedAsync(
                    _currentPage, _pageSize, searchText, departmentId, folderId,
                    organizationId, documentType, fromDate, toDate, documentNumber);

                _totalRecords = totalCount;
                _totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);

                var documentsWithRowNumbers = documents.Select((d, index) => new DocumentViewModel
                {
                    RowNumber = (_currentPage - 1) * _pageSize + index + 1,
                    Id = d.Id,
                    DocumentNumber = d.DocumentNumber,
                    DocumentDate = d.DocumentDate,
                    Subject = d.Subject,
                    TypeText = d.Type == DocumentType.Incoming ? "وارد" : "صادر",
                    Department = d.Department,
                    Folder = d.Folder,
                    Organization = d.Organization,
                    ArchiveSequence = d.ArchiveSequence,
                    OriginalDocument = d
                }).ToList();

                DocumentsDataGrid.ItemsSource = documentsWithRowNumbers;
                DocumentsCountText.Text = $"({_totalRecords} وثيقة)";

                UpdatePaginationInfo();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تحميل الوثائق");
                DocumentsDataGrid.ItemsSource = new List<DocumentViewModel>();
                DocumentsCountText.Text = "(0 وثيقة)";
            }
        }

        private void AddDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addDocumentWindow = new AddDocumentWindow(_databaseService);
                addDocumentWindow.DocumentAdded += async (s, args) =>
                {
                    // تحديث قائمة الوثائق بعد إضافة وثيقة جديدة
                    await LoadDocuments();
                    await LoadStatistics(); // تحديث الإحصائيات أيضاً
                };
                addDocumentWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "فتح نافذة إضافة الوثيقة");
            }
        }

        // طرق مساعدة للحصول على القيم المحددة في الفلاتر
        private int? GetSelectedDepartmentId()
        {
            if (DepartmentFilterComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
                return (int)item.Tag;
            return null;
        }

        private int? GetSelectedFolderId()
        {
            if (FolderFilterComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
                return (int)item.Tag;
            return null;
        }

        private int? GetSelectedOrganizationId()
        {
            if (OrganizationFilterComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
                return (int)item.Tag;
            return null;
        }

        private DocumentType? GetSelectedDocumentType()
        {
            if (TypeFilterComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
            {
                var tag = item.Tag.ToString();
                return tag switch
                {
                    "Outgoing" => DocumentType.Outgoing,
                    "Incoming" => DocumentType.Incoming,
                    _ => null
                };
            }
            return null;
        }

        private void UpdatePaginationInfo()
        {
            try
            {
                TotalRecordsText.Text = _totalRecords.ToString();
                PageInfoText.Text = $"صفحة {_currentPage} من {Math.Max(1, _totalPages)}";

                // تحديث حالة الأزرار
                FirstPageButton.IsEnabled = _currentPage > 1;
                PreviousPageButton.IsEnabled = _currentPage > 1;
                NextPageButton.IsEnabled = _currentPage < _totalPages;
                LastPageButton.IsEnabled = _currentPage < _totalPages;
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تحديث معلومات التصفح: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Event Handlers للبحث والفلترة
        private void QuickSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                // تجنب التحديث أثناء التهيئة
                if (!IsLoaded) return;

                // إلغاء المؤقت السابق إن وجد واستخدام ResourceManager
                _resourceManager.UnregisterTimer("SearchTimer");

                // إنشاء مؤقت جديد للتأخير في البحث (تحسين الأداء)
                var searchTimer = _resourceManager.CreateTimer("SearchTimer", async _ =>
                {
                    await Dispatcher.InvokeAsync(async () =>
                    {
                        _currentPage = 1;
                        await ErrorHandler.ExecuteWithErrorHandlingAsync(LoadDocuments, "البحث السريع");
                    });
                }, null, TimeSpan.FromMilliseconds(500), Timeout.InfiniteTimeSpan);
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "البحث السريع");
            }
        }

        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AdvancedFiltersPanel.Visibility = AdvancedFiltersPanel.Visibility == Visibility.Visible
                    ? Visibility.Collapsed : Visibility.Visible;
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في عرض الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _currentPage = 1;
                await LoadData();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في التحديث: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ComboBoxFilterChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تجنب التحديث أثناء التهيئة
                if (!IsLoaded) return;

                _currentPage = 1;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DateFilterChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تجنب التحديث أثناء التهيئة
                if (!IsLoaded) return;

                _currentPage = 1;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TextFilterChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                // تجنب التحديث أثناء التهيئة
                if (!IsLoaded) return;

                _currentPage = 1;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ApplyFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _currentPage = 1;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                QuickSearchTextBox.Text = "";
                YearFilterComboBox.SelectedIndex = 0;
                DepartmentFilterComboBox.SelectedIndex = 0;
                FolderFilterComboBox.SelectedIndex = 0;
                OrganizationFilterComboBox.SelectedIndex = 0;
                TypeFilterComboBox.SelectedIndex = 0;
                FromDatePicker.SelectedDate = null;
                ToDatePicker.SelectedDate = null;
                DocumentNumberFilterTextBox.Text = "";

                _currentPage = 1;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في مسح الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // طرق التصدير
        private async void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new WpfSaveFileDialog
                {
                    Title = "حفظ ملف Excel",
                    Filter = "ملفات Excel|*.xlsx",
                    FileName = $"الوثائق_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportExcelButton.IsEnabled = false;
                    ExportExcelButton.Content = "جاري التصدير...";

                    await ExportToExcel(saveFileDialog.FileName);

                    WpfMessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{saveFileDialog.FileName}", "نجح التصدير",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ExportExcelButton.IsEnabled = true;
                // إعادة تعيين المحتوى الأصلي للزر
                var originalContent = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                originalContent.Children.Add(new TextBlock { Text = "📊", FontSize = 16, VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(0, 0, 5, 0) });
                originalContent.Children.Add(new TextBlock { Text = "تصدير Excel", Margin = new Thickness(5, 0, 0, 0) });
                ExportExcelButton.Content = originalContent;
            }
        }

        private async void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new WpfSaveFileDialog
                {
                    Title = "حفظ ملف PDF",
                    Filter = "ملفات PDF|*.pdf",
                    FileName = $"تقرير_الوثائق_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportPdfButton.IsEnabled = false;
                    ExportPdfButton.Content = "جاري التصدير...";

                    await ExportToPdf(saveFileDialog.FileName);

                    WpfMessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveFileDialog.FileName}", "نجح التصدير",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.ShowError(ex, "تصدير PDF");
            }
            finally
            {
                ExportPdfButton.IsEnabled = true;
                // إعادة تعيين المحتوى الأصلي للزر
                var originalContent = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                originalContent.Children.Add(new TextBlock { Text = "📄", FontSize = 16, VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(0, 0, 5, 0) });
                originalContent.Children.Add(new TextBlock { Text = "تصدير PDF", Margin = new Thickness(5, 0, 0, 0) });
                ExportPdfButton.Content = originalContent;
            }
        }

        private async Task ExportToExcel(string filePath)
        {
            try
            {
                // الحصول على جميع الوثائق مع الفلاتر المطبقة
                var searchText = QuickSearchTextBox?.Text;
                var departmentId = GetSelectedDepartmentId();
                var folderId = GetSelectedFolderId();
                var organizationId = GetSelectedOrganizationId();
                var documentType = GetSelectedDocumentType();
                var fromDate = FromDatePicker?.SelectedDate;
                var toDate = ToDatePicker?.SelectedDate;
                var documentNumber = DocumentNumberFilterTextBox?.Text;

                // الحصول على جميع الوثائق (بدون تصفح)
                var (documents, _) = await _databaseService.GetDocumentsPagedAsync(
                    1, int.MaxValue, searchText, departmentId, folderId,
                    organizationId, documentType, fromDate, toDate, documentNumber);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("الوثائق");

                // إضافة العناوين
                worksheet.Cell(1, 1).Value = "م";
                worksheet.Cell(1, 2).Value = "رقم الكتاب";
                worksheet.Cell(1, 3).Value = "التاريخ";
                worksheet.Cell(1, 4).Value = "الموضوع";
                worksheet.Cell(1, 5).Value = "النوع";
                worksheet.Cell(1, 6).Value = "من/إلى";
                worksheet.Cell(1, 7).Value = "القسم";
                worksheet.Cell(1, 8).Value = "الضبارة";
                worksheet.Cell(1, 9).Value = "الجهة";
                worksheet.Cell(1, 10).Value = "تسلسل الحفظ";
                worksheet.Cell(1, 11).Value = "ملاحظات";
                worksheet.Cell(1, 12).Value = "تاريخ الإنشاء";

                // تنسيق العناوين
                var headerRange = worksheet.Range(1, 1, 1, 12);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
                headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;

                // إضافة البيانات
                int row = 2;
                foreach (var document in documents)
                {
                    worksheet.Cell(row, 1).Value = row - 1;
                    worksheet.Cell(row, 2).Value = document.DocumentNumber;
                    worksheet.Cell(row, 3).Value = document.DocumentDate.ToString("dd/MM/yyyy");
                    worksheet.Cell(row, 4).Value = document.Subject;
                    worksheet.Cell(row, 5).Value = document.Type == DocumentType.Incoming ? "وارد" : "صادر";
                    worksheet.Cell(row, 6).Value = document.FromTo ?? "";
                    worksheet.Cell(row, 7).Value = document.Department?.Name ?? "";
                    worksheet.Cell(row, 8).Value = document.Folder?.Name ?? "";
                    worksheet.Cell(row, 9).Value = document.Organization?.Name ?? "";
                    worksheet.Cell(row, 10).Value = document.ArchiveSequence ?? "";
                    worksheet.Cell(row, 11).Value = document.Notes ?? "";
                    worksheet.Cell(row, 12).Value = document.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                    row++;
                }

                // تنسيق الجدول
                worksheet.Columns().AdjustToContents();
                var dataRange = worksheet.RangeUsed();
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                // تنسيق الأعمدة
                worksheet.Column(3).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center; // التاريخ
                worksheet.Column(5).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center; // النوع
                worksheet.Column(12).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center; // تاريخ الإنشاء

                // إضافة معلومات إضافية
                var infoRow = row + 2;
                worksheet.Cell(infoRow, 1).Value = "تم التصدير في:";
                worksheet.Cell(infoRow, 2).Value = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
                worksheet.Cell(infoRow + 1, 1).Value = "إجمالي الوثائق:";
                worksheet.Cell(infoRow + 1, 2).Value = documents.Count;

                // حفظ الملف
                workbook.SaveAs(filePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف Excel: {ex.Message}");
            }
        }

        private async Task ExportToPdf(string filePath)
        {
            try
            {
                // الحصول على جميع الوثائق مع الفلاتر المطبقة
                var searchText = QuickSearchTextBox?.Text;
                var departmentId = GetSelectedDepartmentId();
                var folderId = GetSelectedFolderId();
                var organizationId = GetSelectedOrganizationId();
                var documentType = GetSelectedDocumentType();
                var fromDate = FromDatePicker?.SelectedDate;
                var toDate = ToDatePicker?.SelectedDate;
                var documentNumber = DocumentNumberFilterTextBox?.Text;

                // الحصول على جميع الوثائق (بدون تصفح)
                var (documentsData, totalCount) = await _databaseService.GetDocumentsPagedAsync(
                    1, int.MaxValue, searchText, departmentId, folderId,
                    organizationId, documentType, fromDate, toDate, documentNumber);

                // تحويل إلى DocumentViewModel
                var documents = documentsData.Select((doc, index) => new DocumentViewModel
                {
                    RowNumber = index + 1,
                    Id = doc.Id,
                    DocumentNumber = doc.DocumentNumber,
                    DocumentDate = doc.DocumentDate,
                    Subject = doc.Subject,
                    TypeText = doc.Type == DocumentType.Incoming ? "وارد" : "صادر",
                    Department = doc.Department,
                    Folder = doc.Folder,
                    Organization = doc.Organization,
                    ArchiveSequence = doc.ArchiveSequence,
                    OriginalDocument = doc
                }).ToList();

                // إنشاء تقرير PDF بسيط باستخدام HTML
                var htmlContent = GenerateHtmlReport(documents, totalCount);

                // حفظ HTML مؤقتاً
                var tempHtmlPath = Path.GetTempFileName() + ".html";
                await File.WriteAllTextAsync(tempHtmlPath, htmlContent, System.Text.Encoding.UTF8);

                // فتح HTML في المتصفح للطباعة كـ PDF
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempHtmlPath,
                    UseShellExecute = true
                });

                WpfMessageBox.Show("تم فتح التقرير في المتصفح. يمكنك طباعته كـ PDF من خلال Ctrl+P واختيار 'حفظ كـ PDF'",
                                  "تصدير PDF", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير PDF: {ex.Message}");
            }
        }

        private string GenerateHtmlReport(List<DocumentViewModel> documents, int totalCount)
        {
            var html = $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تقرير الوثائق</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 20px;
        }}
        .header h1 {{
            color: #4A90E2;
            margin: 0;
            font-size: 28px;
        }}
        .header p {{
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }}
        .summary {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-right: 4px solid #28a745;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 12px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }}
        th {{
            background-color: #4A90E2;
            color: white;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        .footer {{
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }}
        @media print {{
            body {{ margin: 0; }}
            .header {{ page-break-after: avoid; }}
        }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>📄 تقرير الوثائق</h1>
        <p>تم إنشاؤه في: {DateTime.Now:dd/MM/yyyy HH:mm:ss}</p>
    </div>

    <div class='summary'>
        <h3>📊 ملخص التقرير</h3>
        <p><strong>إجمالي الوثائق:</strong> {totalCount} وثيقة</p>
        <p><strong>تاريخ التقرير:</strong> {DateTime.Now:dd/MM/yyyy}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>م</th>
                <th>رقم الكتاب</th>
                <th>التاريخ</th>
                <th>الموضوع</th>
                <th>النوع</th>
                <th>القسم</th>
                <th>الضبارة</th>
                <th>الجهة</th>
                <th>تسلسل الحفظ</th>
            </tr>
        </thead>
        <tbody>";

            int rowNumber = 1;
            foreach (var document in documents)
            {
                html += $@"
            <tr>
                <td>{rowNumber}</td>
                <td>{document.DocumentNumber}</td>
                <td>{document.DocumentDate:dd/MM/yyyy}</td>
                <td>{document.Subject}</td>
                <td>{document.TypeText}</td>
                <td>{document.Department?.Name ?? ""}</td>
                <td>{document.Folder?.Name ?? ""}</td>
                <td>{document.Organization?.Name ?? ""}</td>
                <td>{document.ArchiveSequence ?? ""}</td>
            </tr>";
                rowNumber++;
            }

            html += $@"
        </tbody>
    </table>

    <div class='footer'>
        <p>تم إنشاء هذا التقرير بواسطة نظام الأرشفة الإلكترونية</p>
        <p>© {DateTime.Now.Year} - جميع الحقوق محفوظة</p>
    </div>
</body>
</html>";

            return html;
        }

        // طرق التصفح (Pagination)
        private async void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (!IsLoaded) return;

                if (PageSizeComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
                {
                    _pageSize = int.Parse(item.Tag.ToString()!);
                    _currentPage = 1;
                    await LoadDocuments();
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تغيير حجم الصفحة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void FirstPageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _currentPage = 1;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في الانتقال للصفحة الأولى: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PreviousPageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentPage > 1)
                {
                    _currentPage--;
                    await LoadDocuments();
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في الانتقال للصفحة السابقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentPage < _totalPages)
                {
                    _currentPage++;
                    await LoadDocuments();
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في الانتقال للصفحة التالية: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LastPageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _currentPage = _totalPages;
                await LoadDocuments();
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في الانتقال للصفحة الأخيرة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // طرق العمليات على الوثائق
        private async void DocumentsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (DocumentsDataGrid.SelectedItem is DocumentViewModel documentVM)
                {
                    var document = await _databaseService.GetDocumentByIdAsync(documentVM.Id);
                    if (document != null)
                    {
                        var viewWindow = new ViewDocumentWindow(_databaseService, document);
                        viewWindow.DocumentUpdated += async (s, args) =>
                        {
                            await LoadDocuments();
                            await LoadStatistics();
                        };
                        viewWindow.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في عرض الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ViewDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is DocumentViewModel documentVM)
                {
                    var document = await _databaseService.GetDocumentByIdAsync(documentVM.Id);
                    if (document != null)
                    {
                        var viewWindow = new ViewDocumentWindow(_databaseService, document);
                        viewWindow.DocumentUpdated += async (s, args) =>
                        {
                            await LoadDocuments();
                            await LoadStatistics();
                        };
                        viewWindow.ShowDialog();
                    }
                    else
                    {
                        WpfMessageBox.Show("لم يتم العثور على الوثيقة", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في عرض الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void EditDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is DocumentViewModel documentVM)
                {
                    var document = await _databaseService.GetDocumentByIdAsync(documentVM.Id);
                    if (document != null)
                    {
                        var editWindow = new EditDocumentWindow(_databaseService, document);
                        editWindow.DocumentUpdated += async (s, args) =>
                        {
                            await LoadDocuments();
                            await LoadStatistics();
                        };
                        editWindow.ShowDialog();
                    }
                    else
                    {
                        WpfMessageBox.Show("لم يتم العثور على الوثيقة", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في تعديل الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is DocumentViewModel documentVM)
                {
                    var result = WpfMessageBox.Show(
                        $"هل أنت متأكد من حذف الوثيقة '{documentVM.DocumentNumber}'؟\n\nسيتم حذف الوثيقة وجميع مرفقاتها نهائياً.",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        var document = await _databaseService.GetDocumentByIdAsync(documentVM.Id);
                        if (document != null)
                        {
                            // حذف جميع مرفقات الوثيقة
                            var attachments = await _databaseService.GetAttachmentsByDocumentIdAsync(document.Id);
                            foreach (var attachment in attachments)
                            {
                                await _databaseService.DeleteAttachmentFileAsync(attachment.Id);
                            }

                            // حذف الوثيقة
                            await _databaseService.DeleteDocumentAsync(document.Id);

                            WpfMessageBox.Show("تم حذف الوثيقة بنجاح", "نجح",
                                          MessageBoxButton.OK, MessageBoxImage.Information);

                            // تحديث القائمة
                            await LoadDocuments();
                            await LoadStatistics();
                        }
                        else
                        {
                            WpfMessageBox.Show("لم يتم العثور على الوثيقة", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WpfMessageBox.Show($"خطأ في حذف الوثيقة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintDocumentButton_Click(object sender, RoutedEventArgs e)
        {
            WpfMessageBox.Show("وظيفة طباعة الوثيقة ستكون متاحة قريباً", "قريباً",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // تنظيف الموارد
        private void UserControl_Unloaded(object sender, RoutedEventArgs e)
        {
            _searchTimer?.Dispose();
        }
    }

    /// <summary>
    /// نموذج عرض الوثيقة مع رقم الصف
    /// </summary>
    public class DocumentViewModel
    {
        public int RowNumber { get; set; }
        public int Id { get; set; }
        public string DocumentNumber { get; set; } = string.Empty;
        public DateTime DocumentDate { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string TypeText { get; set; } = string.Empty;
        public Department? Department { get; set; }
        public Folder? Folder { get; set; }
        public Organization? Organization { get; set; }
        public string? ArchiveSequence { get; set; }
        public Document? OriginalDocument { get; set; }
    }

    /// <summary>
    /// تنظيف الموارد ومنع تسريب الذاكرة
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                // تنظيف جميع الموارد المسجلة
                _resourceManager?.Dispose();

                // إلغاء الاشتراك في الأحداث
                Loaded -= HomePage_Loaded;

                _disposed = true;
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError(ex, "تنظيف موارد الصفحة الرئيسية");
            }
        }
    }

    private void UserControl_Unloaded(object sender, RoutedEventArgs e)
    {
        Dispose();
    }
}
