using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace Archif.Helpers
{
    /// <summary>
    /// محسن أداء واجهة المستخدم
    /// </summary>
    public static class UIPerformanceOptimizer
    {
        private static readonly DispatcherTimer _memoryCleanupTimer;
        private static readonly Dictionary<string, DateTime> _lastUpdateTimes = new();

        static UIPerformanceOptimizer()
        {
            _memoryCleanupTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5)
            };
            _memoryCleanupTimer.Tick += (s, e) => CleanupMemory();
            _memoryCleanupTimer.Start();
        }

        /// <summary>
        /// تحسين DataGrid للأداء العالي
        /// </summary>
        public static void OptimizeDataGrid(DataGrid dataGrid)
        {
            // تفعيل Virtualization
            dataGrid.EnableRowVirtualization = true;
            dataGrid.EnableColumnVirtualization = true;
            
            // تحسين التمرير
            dataGrid.ScrollViewer.CanContentScroll = true;
            dataGrid.ScrollViewer.IsDeferredScrollingEnabled = true;
            
            // تحسين التحديد
            dataGrid.SelectionMode = DataGridSelectionMode.Single;
            dataGrid.SelectionUnit = DataGridSelectionUnit.FullRow;
            
            // تحسين الرسم
            RenderOptions.SetBitmapScalingMode(dataGrid, BitmapScalingMode.LowQuality);
            RenderOptions.SetCachingHint(dataGrid, CachingHint.Cache);
            
            // تأخير التحديث
            dataGrid.BeginningEdit += (s, e) => 
            {
                if (ShouldThrottleUpdate("datagrid_edit"))
                    e.Cancel = true;
            };
        }

        /// <summary>
        /// تحسين ListView للأداء العالي
        /// </summary>
        public static void OptimizeListView(ListView listView)
        {
            // تفعيل Virtualization
            VirtualizingPanel.SetIsVirtualizing(listView, true);
            VirtualizingPanel.SetVirtualizationMode(listView, VirtualizationMode.Recycling);
            VirtualizingPanel.SetIsContainerVirtualizable(listView, true);
            
            // تحسين التمرير
            VirtualizingPanel.SetScrollUnit(listView, ScrollUnit.Item);
            
            // تحسين الرسم
            RenderOptions.SetBitmapScalingMode(listView, BitmapScalingMode.LowQuality);
        }

        /// <summary>
        /// تحسين ComboBox للبيانات الكبيرة
        /// </summary>
        public static void OptimizeComboBox(ComboBox comboBox)
        {
            // تفعيل Virtualization
            VirtualizingPanel.SetIsVirtualizing(comboBox, true);
            VirtualizingPanel.SetVirtualizationMode(comboBox, VirtualizationMode.Recycling);
            
            // تحسين البحث
            comboBox.IsTextSearchEnabled = true;
            comboBox.IsEditable = false; // لتحسين الأداء
        }

        /// <summary>
        /// تحديث مؤجل للتحكمات
        /// </summary>
        public static void DeferredUpdate(string key, Action updateAction, int delayMs = 300)
        {
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(delayMs)
            };
            
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                updateAction?.Invoke();
            };
            
            timer.Start();
        }

        /// <summary>
        /// تحديث مجمع للبيانات
        /// </summary>
        public static void BatchUpdate<T>(ObservableCollection<T> collection, IEnumerable<T> newItems)
        {
            if (collection is INotifyPropertyChanged notifyCollection)
            {
                // تعطيل الإشعارات مؤقتاً
                var descriptor = TypeDescriptor.GetProperties(notifyCollection)["SuppressNotification"];
                descriptor?.SetValue(notifyCollection, true);
            }

            collection.Clear();
            foreach (var item in newItems)
            {
                collection.Add(item);
            }

            if (collection is INotifyPropertyChanged notifyCollection2)
            {
                // إعادة تفعيل الإشعارات
                var descriptor = TypeDescriptor.GetProperties(notifyCollection2)["SuppressNotification"];
                descriptor?.SetValue(notifyCollection2, false);
            }
        }

        /// <summary>
        /// تحسين تحميل الصور
        /// </summary>
        public static void OptimizeImageLoading(Image image, string imagePath)
        {
            if (!File.Exists(imagePath))
                return;

            // تحميل مؤجل
            image.Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() =>
            {
                try
                {
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath);
                    bitmap.DecodePixelWidth = 200; // تحديد حجم أقصى
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze(); // لتحسين الأداء
                    
                    image.Source = bitmap;
                }
                catch
                {
                    // تجاهل أخطاء تحميل الصور
                }
            }));
        }

        /// <summary>
        /// تحسين البحث المباشر
        /// </summary>
        public static void OptimizeLiveSearch(TextBox searchBox, Action<string> searchAction)
        {
            var searchTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };

            searchTimer.Tick += (s, e) =>
            {
                searchTimer.Stop();
                searchAction?.Invoke(searchBox.Text);
            };

            searchBox.TextChanged += (s, e) =>
            {
                searchTimer.Stop();
                searchTimer.Start();
            };
        }

        /// <summary>
        /// تحسين التمرير السلس
        /// </summary>
        public static void EnableSmoothScrolling(ScrollViewer scrollViewer)
        {
            scrollViewer.PanningMode = PanningMode.VerticalOnly;
            scrollViewer.PanningDeceleration = 0.1;
            scrollViewer.PanningRatio = 1.0;
        }

        /// <summary>
        /// تقليل استهلاك الذاكرة للنوافذ
        /// </summary>
        public static void OptimizeWindow(Window window)
        {
            // تحسين الرسم
            RenderOptions.SetBitmapScalingMode(window, BitmapScalingMode.LowQuality);
            RenderOptions.SetCachingHint(window, CachingHint.Cache);
            
            // تحسين التخطيط
            window.UseLayoutRounding = true;
            window.SnapsToDevicePixels = true;
            
            // تنظيف الموارد عند الإغلاق
            window.Closed += (s, e) =>
            {
                CleanupWindowResources(window);
            };
        }

        /// <summary>
        /// تنظيف موارد النافذة
        /// </summary>
        private static void CleanupWindowResources(Window window)
        {
            try
            {
                // تنظيف الموارد المرتبطة بالنافذة
                if (window.DataContext is IDisposable disposableContext)
                {
                    disposableContext.Dispose();
                }

                // تنظيف الأحداث
                window.DataContext = null;
                
                // إجبار تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }

        /// <summary>
        /// تحديد ما إذا كان يجب تأخير التحديث
        /// </summary>
        private static bool ShouldThrottleUpdate(string key)
        {
            var now = DateTime.Now;
            if (_lastUpdateTimes.TryGetValue(key, out var lastUpdate))
            {
                if (now.Subtract(lastUpdate).TotalMilliseconds < 100)
                {
                    return true;
                }
            }
            
            _lastUpdateTimes[key] = now;
            return false;
        }

        /// <summary>
        /// تنظيف دوري للذاكرة
        /// </summary>
        private static void CleanupMemory()
        {
            try
            {
                // تنظيف الكاش المؤقت
                _lastUpdateTimes.Clear();
                
                // إجبار تنظيف الذاكرة
                GC.Collect(2, GCCollectionMode.Optimized);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Optimized);
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }

        /// <summary>
        /// مراقب أداء الواجهة
        /// </summary>
        public class UIPerformanceMonitor
        {
            private readonly Dictionary<string, List<TimeSpan>> _performanceData = new();
            private readonly object _lock = new object();

            public void RecordOperation(string operationName, TimeSpan duration)
            {
                lock (_lock)
                {
                    if (!_performanceData.ContainsKey(operationName))
                    {
                        _performanceData[operationName] = new List<TimeSpan>();
                    }
                    
                    _performanceData[operationName].Add(duration);
                    
                    // الاحتفاظ بآخر 100 قياس فقط
                    if (_performanceData[operationName].Count > 100)
                    {
                        _performanceData[operationName].RemoveAt(0);
                    }
                }
            }

            public TimeSpan GetAverageTime(string operationName)
            {
                lock (_lock)
                {
                    if (_performanceData.TryGetValue(operationName, out var times) && times.Any())
                    {
                        return TimeSpan.FromTicks((long)times.Average(t => t.Ticks));
                    }
                    return TimeSpan.Zero;
                }
            }

            public Dictionary<string, TimeSpan> GetAllAverages()
            {
                lock (_lock)
                {
                    return _performanceData.ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value.Any() ? TimeSpan.FromTicks((long)kvp.Value.Average(t => t.Ticks)) : TimeSpan.Zero
                    );
                }
            }
        }

        /// <summary>
        /// مساعد لقياس الأداء
        /// </summary>
        public static IDisposable MeasurePerformance(string operationName, UIPerformanceMonitor monitor = null)
        {
            return new PerformanceMeasurer(operationName, monitor);
        }

        private class PerformanceMeasurer : IDisposable
        {
            private readonly string _operationName;
            private readonly UIPerformanceMonitor _monitor;
            private readonly DateTime _startTime;

            public PerformanceMeasurer(string operationName, UIPerformanceMonitor monitor)
            {
                _operationName = operationName;
                _monitor = monitor;
                _startTime = DateTime.Now;
            }

            public void Dispose()
            {
                var duration = DateTime.Now - _startTime;
                _monitor?.RecordOperation(_operationName, duration);
                
                // تسجيل العمليات البطيئة
                if (duration.TotalMilliseconds > 500)
                {
                    System.Diagnostics.Debug.WriteLine($"عملية بطيئة: {_operationName} استغرقت {duration.TotalMilliseconds:F2} مللي ثانية");
                }
            }
        }
    }
}
