<UserControl x:Class="Archif.Views.SettingsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- النسخ الاحتياطي -->
            <Border Grid.Row="0" Margin="0,0,0,20" Padding="25"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                        <Border Width="32" Height="32" CornerRadius="16" VerticalAlignment="Center">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#4A90E2" Offset="0"/>
                                    <GradientStop Color="#357ABD" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <TextBlock Text="💾" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                        </Border>
                        <TextBlock Text="النسخ الاحتياطي"
                                  FontSize="18"
                                  FontWeight="Bold"
                                  Margin="15,0,0,0"
                                  VerticalAlignment="Center"
                                  Foreground="#333333"/>
                    </StackPanel>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="إنشاء نسخة احتياطية من قاعدة البيانات"
                                      Margin="0,0,0,10"
                                      Foreground="#666666"/>

                            <StackPanel Orientation="Horizontal">
                                <TextBox x:Name="BackupPathTextBox"
                                        Height="35"
                                        Width="400"
                                        Padding="10,8"
                                        IsReadOnly="True"
                                        BorderBrush="#E0E0E0"
                                        BorderThickness="1"
                                        Background="#F8F9FA"/>

                                <Button x:Name="BrowseBackupPathButton"
                                       Content="استعراض"
                                       Background="White"
                                       Foreground="#4A90E2"
                                       BorderBrush="#4A90E2"
                                       BorderThickness="1"
                                       Padding="15,8"
                                       Margin="10,0,0,0"
                                       Click="BrowseBackupPathButton_Click"/>
                            </StackPanel>
                        </StackPanel>

                        <Button Grid.Column="1"
                               x:Name="CreateBackupButton"
                               Background="#4A90E2"
                               Foreground="White"
                               BorderThickness="0"
                               Padding="20,10"
                               VerticalAlignment="Bottom"
                               Click="CreateBackupButton_Click">
                            <Button.Effect>
                                <DropShadowEffect Color="#4A90E2" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💾" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="إنشاء نسخة احتياطية" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- استعادة البيانات -->
            <Border Grid.Row="1" Margin="0,0,0,20" Padding="25"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                        <Border Width="32" Height="32" CornerRadius="16" VerticalAlignment="Center">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#DC3545" Offset="0"/>
                                    <GradientStop Color="#C82333" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <TextBlock Text="🔄" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                        </Border>
                        <TextBlock Text="استعادة البيانات"
                                  FontSize="18"
                                  FontWeight="Bold"
                                  Margin="15,0,0,0"
                                  VerticalAlignment="Center"
                                  Foreground="#333333"/>
                    </StackPanel>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="استعادة البيانات من نسخة احتياطية"
                                      Margin="0,0,0,10"
                                      Foreground="#666666"/>

                            <StackPanel Orientation="Horizontal">
                                <TextBox x:Name="RestorePathTextBox"
                                        Height="35"
                                        Width="400"
                                        Padding="10,8"
                                        IsReadOnly="True"
                                        BorderBrush="#E0E0E0"
                                        BorderThickness="1"
                                        Background="#F8F9FA"/>

                                <Button x:Name="BrowseRestorePathButton"
                                       Content="استعراض"
                                       Background="White"
                                       Foreground="#DC3545"
                                       BorderBrush="#DC3545"
                                       BorderThickness="1"
                                       Padding="15,8"
                                       Margin="10,0,0,0"
                                       Click="BrowseRestorePathButton_Click"/>
                            </StackPanel>
                        </StackPanel>

                        <Button Grid.Column="1"
                               x:Name="RestoreButton"
                               Background="#DC3545"
                               Foreground="White"
                               BorderThickness="0"
                               Padding="20,10"
                               VerticalAlignment="Bottom"
                               Click="RestoreButton_Click">
                            <Button.Effect>
                                <DropShadowEffect Color="#DC3545" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔄" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="استعادة البيانات" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- إعدادات أخرى -->
            <Border Grid.Row="2" Padding="25"
                   Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                        <Border Width="32" Height="32" CornerRadius="16" VerticalAlignment="Center">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#6C757D" Offset="0"/>
                                    <GradientStop Color="#5A6268" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <TextBlock Text="⚙️" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                        </Border>
                        <TextBlock Text="إعدادات عامة ومعلومات التطبيق"
                                  FontSize="18"
                                  FontWeight="Bold"
                                  Margin="15,0,0,0"
                                  VerticalAlignment="Center"
                                  Foreground="#333333"/>
                    </StackPanel>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- الوضع الداكن -->
                        <StackPanel Grid.Row="0"
                                   Orientation="Horizontal"
                                   Margin="0,0,0,20">
                            <CheckBox x:Name="DarkModeToggle"
                                     VerticalAlignment="Center"
                                     IsEnabled="False"/>
                            <TextBlock Text="الوضع الداكن (قريباً)"
                                      FontSize="16"
                                      Margin="15,0,0,0"
                                      VerticalAlignment="Center"
                                      Foreground="#666666"/>
                        </StackPanel>

                        <!-- معلومات قاعدة البيانات -->
                        <StackPanel Grid.Row="1" Margin="0,0,0,20">
                            <TextBlock Text="معلومات قاعدة البيانات:"
                                      FontWeight="Bold"
                                      Margin="0,0,0,10"
                                      Foreground="#333333"/>
                            <TextBlock x:Name="DatabaseInfoText"
                                      Text="مسار قاعدة البيانات: ..."
                                      Foreground="#666666"/>
                        </StackPanel>

                        <!-- معلومات النسخة -->
                        <StackPanel Grid.Row="2" Margin="0,0,0,25">
                            <TextBlock Text="معلومات التطبيق:"
                                      FontWeight="Bold"
                                      Margin="0,0,0,10"
                                      Foreground="#333333"/>
                            <TextBlock Text="نظام الأرشفة الإلكترونية - الإصدار 1.0.0"
                                      Foreground="#666666"
                                      Margin="0,0,0,5"/>
                            <TextBlock Text="تم التطوير باستخدام WPF و .NET 6.0"
                                      Foreground="#666666"/>
                        </StackPanel>

                        <!-- معلومات المطور -->
                        <StackPanel Grid.Row="3">
                            <Border Padding="20" Background="#F8F9FA" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1">
                                <Border.Effect>
                                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.2"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <Border Width="28" Height="28" CornerRadius="14" VerticalAlignment="Center">
                                            <Border.Background>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                    <GradientStop Color="#28A745" Offset="0"/>
                                                    <GradientStop Color="#1E7E34" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Border.Background>
                                            <TextBlock Text="👨‍💻" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="معلومات المطور"
                                                  FontSize="16"
                                                  FontWeight="Bold"
                                                  Margin="12,0,0,0"
                                                  VerticalAlignment="Center"
                                                  Foreground="#333333"/>
                                    </StackPanel>

                                    <TextBlock Text="لقد تم صنع هذا البرنامج من قبل المطور علي محمد نهار"
                                              FontSize="14"
                                              Margin="0,0,0,15"
                                              TextAlignment="Center"
                                              FontWeight="SemiBold"
                                              Foreground="#333333"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- رقم الواتساب -->
                                        <Border Grid.Column="0" Margin="0,0,10,0" Padding="15" Background="White"
                                               CornerRadius="6" BorderBrush="#E0E0E0" BorderThickness="1"
                                               Cursor="Hand">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="1" BlurRadius="4" Opacity="0.3"/>
                                            </Border.Effect>
                                            <Border.InputBindings>
                                                <MouseBinding MouseAction="LeftClick" Command="{Binding OpenWhatsAppCommand}"/>
                                            </Border.InputBindings>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <TextBlock Text="📱" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                                <StackPanel>
                                                    <TextBlock Text="واتساب" FontSize="12" FontWeight="Bold" Foreground="#25D366" HorizontalAlignment="Center"/>
                                                    <TextBlock x:Name="WhatsAppLink" Text="9647815883398" FontSize="14"
                                                              Foreground="#4A90E2" HorizontalAlignment="Center"
                                                              MouseLeftButtonDown="WhatsAppLink_Click"
                                                              Cursor="Hand">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Style.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="TextDecorations" Value="Underline"/>
                                                                        <Setter Property="Foreground" Value="#357ABD"/>
                                                                    </Trigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>

                                        <!-- البريد الإلكتروني -->
                                        <Border Grid.Column="1" Margin="10,0,0,0" Padding="15" Background="White"
                                               CornerRadius="6" BorderBrush="#E0E0E0" BorderThickness="1"
                                               Cursor="Hand">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="1" BlurRadius="4" Opacity="0.3"/>
                                            </Border.Effect>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <TextBlock Text="✉️" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                                <StackPanel>
                                                    <TextBlock Text="البريد الإلكتروني" FontSize="12" FontWeight="Bold" Foreground="#DC3545" HorizontalAlignment="Center"/>
                                                    <TextBlock x:Name="EmailLink" Text="<EMAIL>" FontSize="12"
                                                              Foreground="#4A90E2" HorizontalAlignment="Center"
                                                              MouseLeftButtonDown="EmailLink_Click"
                                                              Cursor="Hand">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Style.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="TextDecorations" Value="Underline"/>
                                                                        <Setter Property="Foreground" Value="#357ABD"/>
                                                                    </Trigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <TextBlock Text="للدعم الفني والاستفسارات، يرجى التواصل عبر الواتساب أو البريد الإلكتروني"
                                              FontSize="12"
                                              Margin="0,15,0,0"
                                              TextAlignment="Center"
                                              Foreground="#666666"
                                              FontStyle="Italic"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
