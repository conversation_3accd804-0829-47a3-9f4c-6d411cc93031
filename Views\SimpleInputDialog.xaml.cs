using System.Windows;

namespace Archif.Views
{
    /// <summary>
    /// نافذة إدخال نص بسيطة
    /// </summary>
    public partial class SimpleInputDialog : Window
    {
        public string InputText => InputTextBox.Text;

        public SimpleInputDialog(string title, string prompt, string defaultText = "")
        {
            InitializeComponent();
            
            Title = title;
            PromptTextBlock.Text = prompt;
            InputTextBox.Text = defaultText;
            
            // تحديد النص عند فتح النافذة
            InputTextBox.SelectAll();
            InputTextBox.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(InputTextBox.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال النص المطلوب", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                InputTextBox.Focus();
                return;
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
