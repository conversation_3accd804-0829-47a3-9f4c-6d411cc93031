﻿<Window x:Class="Archif.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Archif"
        mc:Ignorable="d"
        Title="نظام الأرشفة الإلكترونية"
        Height="800" Width="1200"
        MinHeight="600" MinWidth="1000"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI, Tahoma, Arial"
        Background="#F5F5F5">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي -->
            <Border Grid.Column="0"
                    Background="#2E3440"
                    BorderThickness="0,0,1,0"
                    BorderBrush="#E0E0E0">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شعار التطبيق -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal"
                               Margin="20,20,20,30" HorizontalAlignment="Center">
                        <TextBlock Text="📚" FontSize="28" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Text="نظام الأرشفة"
                                  Foreground="White"
                                  FontSize="18" FontWeight="Bold"
                                  Margin="5,0,0,0"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- قائمة التنقل -->
                    <ListBox Grid.Row="1" x:Name="NavigationListBox"
                            Background="Transparent"
                            BorderThickness="0"
                            SelectionChanged="NavigationListBox_SelectionChanged">

                        <ListBoxItem x:Name="HomeItem" IsSelected="True">
                            <StackPanel Orientation="Horizontal" Margin="20,15">
                                <TextBlock Text="🏠" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="الرئيسية"
                                          Margin="5,0,0,0"
                                          VerticalAlignment="Center"
                                          Foreground="White"
                                          FontSize="16"/>
                            </StackPanel>
                        </ListBoxItem>

                        <ListBoxItem x:Name="DepartmentsItem">
                            <StackPanel Orientation="Horizontal" Margin="20,15">
                                <TextBlock Text="📁" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="الأقسام"
                                          Margin="5,0,0,0"
                                          VerticalAlignment="Center"
                                          Foreground="White"
                                          FontSize="16"/>
                            </StackPanel>
                        </ListBoxItem>

                        <ListBoxItem x:Name="OrganizationsItem">
                            <StackPanel Orientation="Horizontal" Margin="20,15">
                                <TextBlock Text="🏛️" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="الجهات"
                                          Margin="5,0,0,0"
                                          VerticalAlignment="Center"
                                          Foreground="White"
                                          FontSize="16"/>
                            </StackPanel>
                        </ListBoxItem>

                        <ListBoxItem x:Name="SettingsItem">
                            <StackPanel Orientation="Horizontal" Margin="20,15">
                                <TextBlock Text="⚙️" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="الإعدادات"
                                          Margin="5,0,0,0"
                                          VerticalAlignment="Center"
                                          Foreground="White"
                                          FontSize="16"/>
                            </StackPanel>
                        </ListBoxItem>
                    </ListBox>

                    <!-- معلومات النسخة -->
                    <StackPanel Grid.Row="2" Margin="20" HorizontalAlignment="Center">
                        <TextBlock Text="الإصدار 1.0.0"
                                  Foreground="White"
                                  FontSize="12"
                                  HorizontalAlignment="Center"
                                  Opacity="0.7"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- المحتوى الرئيسي -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط العنوان -->
                <Border Grid.Row="0"
                       Background="White"
                       BorderThickness="0,0,0,1"
                       BorderBrush="#E0E0E0"
                       Padding="30,20">

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <TextBlock x:Name="PageTitle"
                                      Text="الرئيسية"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      VerticalAlignment="Center"
                                      Foreground="#333333"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock x:Name="CurrentDateTime"
                                      FontSize="14"
                                      VerticalAlignment="Center"
                                      Foreground="#666666"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- منطقة المحتوى -->
                <ContentControl Grid.Row="1" x:Name="MainContentControl"
                               Margin="20"/>
            </Grid>
        </Grid>
</Window>
