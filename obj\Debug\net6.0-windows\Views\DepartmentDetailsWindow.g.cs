﻿#pragma checksum "..\..\..\..\Views\DepartmentDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FC4F184AB0DEE69DDC8509E611E101D28321DA2E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Archif.Views {
    
    
    /// <summary>
    /// DepartmentDetailsWindow
    /// </summary>
    public partial class DepartmentDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 74 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DepartmentNameText;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateText;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FoldersCountText;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentsCountText;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IncomingDocsText;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutgoingDocsText;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFolderButton;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchFoldersTextBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SearchFoldersPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl FoldersItemsControl;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditDepartmentButton;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Archif;V2.0.0.0;component/views/departmentdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DepartmentNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CreatedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.FoldersCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DocumentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.IncomingDocsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.OutgoingDocsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.AddFolderButton = ((System.Windows.Controls.Button)(target));
            
            #line 192 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            this.AddFolderButton.Click += new System.Windows.RoutedEventHandler(this.AddFolderButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SearchFoldersTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 205 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            this.SearchFoldersTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchFoldersTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SearchFoldersPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.FoldersItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 16:
            this.EditDepartmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            this.EditDepartmentButton.Click += new System.Windows.RoutedEventHandler(this.EditDepartmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 322 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 270 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewFolderDocumentsButton_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 280 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditFolderButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 290 "..\..\..\..\Views\DepartmentDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteFolderButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

